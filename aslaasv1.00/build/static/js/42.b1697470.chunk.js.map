{"version": 3, "sources": ["pages/helpPage.js", "components/animate/variants/path.js", "components/animate/variants/transition.js", "components/animate/variants/bounce.js", "components/animate/variants/container.js", "components/animate/MotionContainer.js", "components/animate/IconButtonAnimate.js", "components/Page.js"], "names": ["RootStyle", "styled", "_ref", "theme", "display", "minHeight", "alignItems", "paddingTop", "spacing", "paddingBottom", "SmsContainer", "Box", "_ref2", "flexDirection", "max<PERSON><PERSON><PERSON>", "margin", "backgroundColor", "palette", "background", "paper", "borderRadius", "shape", "boxShadow", "shadows", "padding", "min<PERSON><PERSON><PERSON>", "SmsMessage", "Paper", "_ref3", "isUser", "marginBottom", "alignSelf", "primary", "main", "grey", "color", "contrastText", "text", "HelpPage", "_jsx", "Page", "title", "sx", "height", "children", "Container", "component", "MotionContainer", "_jsxs", "textAlign", "m", "div", "variants", "varBounce", "in", "Typography", "variant", "paragraph", "mb", "src", "alt", "style", "width", "Grid", "container", "justifyContent", "id", "name", "phone", "fbLink", "avatar", "locationLink", "locationTitle", "map", "supporter", "item", "xs", "sm", "md", "p", "border", "borderColor", "Avatar", "fontWeight", "href", "concat", "replace", "target", "rel", "mt", "command", "description", "cmd", "index", "gutterBottom", "embedId", "video", "position", "overflow", "top", "left", "frameBorder", "allow", "allowFullScreen", "question", "answer", "faq", "Accordion", "AccordionSummary", "expandIcon", "ExpandMoreIcon", "AccordionDetails", "<PERSON><PERSON>", "to", "size", "RouterLink", "varTranEnter", "props", "duration", "durationIn", "ease", "easeIn", "varTranExit", "durationOut", "easeOut", "initial", "animate", "scale", "opacity", "transition", "exit", "inUp", "y", "scaleY", "_objectSpread", "inDown", "inLeft", "x", "scaleX", "inRight", "out", "outUp", "outDown", "outLeft", "outRight", "<PERSON><PERSON><PERSON><PERSON>", "stagger<PERSON><PERSON><PERSON><PERSON>", "staggerIn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "staggerDirection", "action", "other", "_objectWithoutProperties", "_excluded", "IconButtonAnimate", "forwardRef", "ref", "AnimateWrap", "IconButton", "propTypes", "PropTypes", "node", "isRequired", "oneOf", "var<PERSON>mall", "hover", "tap", "varMedium", "var<PERSON><PERSON>ge", "isSmall", "is<PERSON>arge", "whileTap", "whileHover", "meta", "_Fragment", "<PERSON><PERSON><PERSON>", "string"], "mappings": "oGAAA,uNAwBA,MAAMA,EAAYC,YAAO,MAAPA,EAAcC,IAAA,IAAC,MAAEC,GAAOD,EAAA,MAAM,CAC9CE,QAAS,OACTC,UAAW,OACXC,WAAY,SACZC,WAAYJ,EAAMK,QAAQ,IAC1BC,cAAeN,EAAMK,QAAQ,GAC9B,IAEKE,EAAeT,YAAOU,IAAPV,EAAYW,IAAA,IAAC,MAAET,GAAOS,EAAA,MAAM,CAC/CR,QAAS,OACTS,cAAe,SACfC,SAAU,OACVC,OAAQ,OACRC,gBAAiBb,EAAMc,QAAQC,WAAWC,MAC1CC,aAAcjB,EAAMkB,MAAMD,aAC1BE,UAAWnB,EAAMoB,QAAQ,GACzBC,QAASrB,EAAMK,QAAQ,GACvBiB,SAAU,IACX,IAEKC,EAAazB,YAAO0B,IAAP1B,EAAc2B,IAAA,IAAC,MAAEzB,EAAK,OAAE0B,GAAQD,EAAA,MAAM,CACvDJ,QAASrB,EAAMK,QAAQ,GACvBY,aAAcjB,EAAMkB,MAAMD,aAC1BU,aAAc3B,EAAMK,QAAQ,KAC5BM,SAAU,MACViB,UAAWF,EAAS,WAAa,aACjCb,gBAAiBa,EAAS1B,EAAMc,QAAQe,QAAQC,KAAO9B,EAAMc,QAAQiB,KAAK,KAC1EC,MAAON,EAAS1B,EAAMc,QAAQe,QAAQI,aAAejC,EAAMc,QAAQoB,KAAKL,QACzE,IAIc,SAASM,IAkItB,OACEC,cAACC,IAAI,CAACC,MAAM,iKAA+BC,GAAI,CAAEC,OAAQ,GAAIC,SAC3DL,cAACvC,EAAS,CAAA4C,SACRL,cAACM,IAAS,CAACC,UAAWC,IAAgBH,SACpCI,eAACrC,IAAG,CAAC+B,GAAI,CAAE5B,SAAU,IAAKC,OAAQ,OAAQkC,UAAW,UAAWL,SAAA,CAE7DI,eAACE,IAAEC,IAAG,CAACC,SAAUC,cAAYC,GAAGV,SAAA,CAC/BL,cAACgB,IAAU,CAACC,QAAQ,KAAKC,WAAS,EAACf,GAAI,CAAEgB,GAAI,GAAId,SAAC,iJAGlDL,cAAA,OACEoB,IAAI,oBACJC,IAAI,qBACJC,MAAO,CAAEC,MAAO,OAAQ1C,aAAc,MAAOU,aAAc,aAK/DkB,eAACE,IAAEC,IAAG,CAACC,SAAUC,cAAYC,GAAGV,SAAA,CAC9BL,cAACgB,IAAU,CAACC,QAAQ,KAAKC,WAAS,EAACf,GAAI,CAAEgB,GAAI,GAAId,SAAC,kRAGlDL,cAACwB,IAAI,CAACC,WAAS,EAACxD,QAAS,EAAGyD,eAAe,SAAQrB,SAtJ5C,CACjB,CACEsB,GAAI,EACJC,KAAM,qBACNC,MAAO,kBACPC,OAAQ,iDACRC,OAAQ,2BACRC,aAAc,wCACdC,cAAe,wHAEjB,CACEN,GAAI,EACJC,KAAM,qBACNC,MAAO,kBACPC,OAAQ,6CACRC,OAAQ,2BACRC,aAAc,wCACdC,cAAe,wHAEjB,CACEN,GAAI,EACJC,KAAM,gEACNC,MAAO,kBACPC,OAAQ,0DACRC,OAAQ,2BACRC,aAAc,wCACdC,cAAe,6EAEjB,CACEN,GAAI,EACJC,KAAM,qBACNC,MAAO,kBACPC,OAAQ,0DACRC,OAAQ,2BACRC,aAAc,wCACdC,cAAe,mFAEjB,CACEN,GAAI,EACJC,KAAM,wCACNC,MAAO,kBACPC,OAAQ,qCACRC,OAAQ,2BACRC,aAAc,wCACdC,cAAe,6EAEjB,CACEN,GAAI,EACJC,KAAM,0GACNC,MAAO,kBACPC,OAAQ,8CACRC,OAAQ,2BACRC,aAAc,wCACdC,cAAe,6EAEjB,CACEN,GAAI,EACJC,KAAM,oDACNC,MAAO,kBACPC,OAAQ,kDACRC,OAAQ,2BACRC,aAAc,wCACdC,cAAe,0FAyFOC,KAAKC,GACfnC,cAACwB,IAAI,CAAoBY,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAGC,GAAI,EAAElC,SACjDI,eAACrC,IAAG,CACF+B,GAAI,CACFtC,QAAS,OACTS,cAAe,SACfP,WAAY,SACZyE,EAAG,EACHC,OAAQ,EACRC,YAAa,WACb7D,aAAc,GACdwB,SAAA,CAEFL,cAAC2C,IAAM,CACLtB,IAAKc,EAAUP,KACfR,IAAKe,EAAUJ,OACf5B,GAAI,CAAEoB,MAAO,GAAInB,OAAQ,GAAIe,GAAI,KAEnCnB,cAACgB,IAAU,CAACC,QAAQ,YAAYd,GAAI,CAAEyC,WAAY,QAASvC,SACxD8B,EAAUP,OAEbnB,eAACO,IAAU,CAACC,QAAQ,QAAQd,GAAI,CAAEP,MAAO,kBAAmBS,SAAA,CAAC,4BACrD,IACNL,cAAA,KAAG6C,KAAI,OAAAC,OAASX,EAAUN,MAAMkB,QAAQ,MAAO,KAAM1C,SAClD8B,EAAUN,WAGfpB,eAACO,IAAU,CAACC,QAAQ,QAAQd,GAAI,CAAEP,MAAO,kBAAmBS,SAAA,CAAC,YACjD,IACVL,cAAA,KAAG6C,KAAMV,EAAUL,OAAQkB,OAAO,SAASC,IAAI,sBAAqB5C,SACjE8B,EAAUL,OAAOiB,QAAQ,eAAgB,SAI7CZ,EAAUH,cACTvB,eAACO,IAAU,CAACC,QAAQ,QAAQd,GAAI,CAAEP,MAAO,kBAAmBS,SAAA,CAAC,4BACrD,IACNL,cAAA,KACE6C,KAAMV,EAAUH,aAChBgB,OAAO,SACPC,IAAI,sBAAqB5C,SAExB8B,EAAUF,eAAiB,oBAzC3BE,EAAUR,WAqD3B3B,cAACW,IAAEC,IAAG,CAACC,SAAUC,cAAYC,GAAIZ,GAAI,CAAE+C,GAAI,GAAI7C,SAC7CL,cAACgB,IAAU,CAACC,QAAQ,KAAKC,WAAS,EAAAb,SAAC,+JAIrCL,cAACgB,IAAU,CAACb,GAAI,CAAEP,MAAO,kBAAmBS,SAAC,uPAI7CL,cAACW,IAAEC,IAAG,CAACC,SAAUC,cAAYC,GAAGV,SAC9BL,cAAC7B,EAAY,CAACgC,GAAI,CAAE+C,GAAI,GAAI7C,SApJvB,CACf,CAAE8C,QAAS,QAASC,YAAa,kHACjC,CAAED,QAAS,KAAMC,YAAa,sGAC9B,CAAED,QAAS,QAASC,YAAa,uWACjC,CAAED,QAAS,OAAQC,YAAa,qLAChC,CAAED,QAAS,SAAUC,YAAa,8LAClC,CAAED,QAAS,SAAUC,YAAa,2MAClC,CAAED,QAAS,SAAUC,YAAa,2bAClC,CAAED,QAAS,UAAWC,YAAa,qQACnC,CAAED,QAAS,MAAOC,YAAa,+NAC/B,CAAED,QAAS,KAAMC,YAAa,gHAC9B,CAAED,QAAS,YAAaC,YAAa,8HACrC,CAAED,QAAS,WAAYC,YAAa,8HACpC,CAAED,QAAS,aAAcC,YAAa,+FACtC,CAAED,QAAS,cAAeC,YAAa,+FACvC,CAAED,QAAS,SAAUC,YAAa,gOAClC,CAAED,QAAS,UAAWC,YAAa,mKACnC,CAAED,QAAS,4CAAeC,YAAa,igBACvC,CAAED,QAAS,YAAaC,YAAa,8gBACrC,CAAED,QAAS,SAAUC,YAAa,oGAClC,CAAED,QAAS,cAAeC,YAAa,miBACvC,CAAED,QAAS,cAAeC,YAAa,miBACvC,CAAED,QAAS,cAAeC,YAAa,oiBA+HjBlB,KAAI,CAACmB,EAAKC,IAClB7C,eAAA,OAAAJ,SAAA,CACEL,cAACb,EAAU,CAACG,QAAM,EAAAe,SAChBL,cAACgB,IAAU,CAACC,QAAQ,QAAQd,GAAI,CAAEyC,WAAY,QAASvC,SACpDgD,EAAIF,YAGTnD,cAACb,EAAU,CAAAkB,SACTL,cAACgB,IAAU,CAACC,QAAQ,QAAOZ,SAAEgD,EAAID,kBAP3BE,SAgBhB7C,eAACE,IAAEC,IAAG,CAACC,SAAUC,cAAYC,GAAGV,SAAA,CAC9BL,cAACgB,IAAU,CAACC,QAAQ,KAAKsC,cAAY,EAACpD,GAAI,CAAE+C,GAAI,GAAI7C,SAAC,kHAGrDL,cAACwB,IAAI,CAACC,WAAS,EAACxD,QAAS,EAAGkC,GAAI,CAAE+C,GAAI,GAAI7C,SAtI/B,CACrB,CAAEsB,GAAI,EAAGzB,MAAO,4EAAiBsD,QAAS,eAC1C,CAAE7B,GAAI,EAAGzB,MAAO,yMAAoDsD,QAAS,eAC7E,CAAE7B,GAAI,EAAGzB,MAAO,qUAAkEsD,QAAS,eAC3F,CAAE7B,GAAI,EAAGzB,MAAO,mFAAmBsD,QAAS,gBAmIhBtB,KAAKuB,GACnBhD,eAACe,IAAI,CAAgBY,MAAI,EAACC,GAAI,GAAGhC,SAAA,CAC/BL,cAACgB,IAAU,CAACC,QAAQ,YAAYd,GAAI,CAAEgB,GAAI,GAAId,SAC3CoD,EAAMvD,QAETF,cAAC5B,IAAG,CACF+B,GAAI,CACFuD,SAAU,WACVxF,cAAe,SACfkC,OAAQ,EACRuD,SAAU,SACV9E,aAAc,EACdE,UAAW,GACXsB,SAEFL,cAAA,UACEE,MAAOuD,EAAMvD,MACboB,MAAO,CACLoC,SAAU,WACVE,IAAK,EACLC,KAAM,EACNtC,MAAO,OACPnB,OAAQ,QAEVgB,IAAG,iCAAA0B,OAAmCW,EAAMD,SAC5CM,YAAY,IACZC,MAAM,iFACNC,iBAAe,QA1BVP,EAAM9B,WAoCvBlB,eAACE,IAAEC,IAAG,CAACC,SAAUC,cAAYC,GAAIZ,GAAI,CAAE+C,GAAI,GAAI7C,SAAA,CAC7CL,cAACgB,IAAU,CAACC,QAAQ,KAAKC,WAAS,EAAAb,SAAC,uIApKlC,CACX,CACE4D,SAAU,mYACVC,OAAQ,+lGAEV,CACED,SAAU,qZACVC,OAAQ,gsCAEV,CACED,SAAU,iLACVC,OAAQ,mlEA4JMhC,KAAI,CAACiC,EAAKb,IACd7C,eAAC2D,IAAS,CAAajE,GAAI,CAAEO,UAAW,QAASL,SAAA,CAC/CL,cAACqE,IAAgB,CAACC,WAAYtE,cAACuE,IAAc,IAAIlE,SAC/CL,cAACgB,IAAU,CAACC,QAAQ,YAAYd,GAAI,CAAEyC,WAAY,QAASvC,SACxD8D,EAAIF,aAGTjE,cAACwE,IAAgB,CAAAnE,SACfL,cAACgB,IAAU,CAACC,QAAQ,QAAOZ,SAAE8D,EAAID,aAPrBZ,QAcpBtD,cAACyE,IAAM,CACLC,GAAG,QACHC,KAAK,QACL1D,QAAQ,YACRV,UAAWqE,IACXzE,GAAI,CAAE+C,GAAI,GAAI7C,SACf,uBAQb,C,2IC5WO,MCOMwE,EAAgBC,IAIpB,CAAEC,UAHa,OAALD,QAAK,IAALA,OAAK,EAALA,EAAOE,aAAc,IAGnBC,MAFD,OAALH,QAAK,IAALA,OAAK,EAALA,EAAOI,SAAU,CAAC,IAAM,IAAM,IAAM,OAKtCC,EAAeL,IAInB,CAAEC,UAHa,OAALD,QAAK,IAALA,OAAK,EAALA,EAAOM,cAAe,IAGpBH,MAFD,OAALH,QAAK,IAALA,OAAK,EAALA,EAAOO,UAAW,CAAC,IAAM,IAAM,IAAM,O,WCd7C,MAAMvE,EAAagE,IACxB,MAAME,EAAkB,OAALF,QAAK,IAALA,OAAK,EAALA,EAAOE,WACpBI,EAAmB,OAALN,QAAK,IAALA,OAAK,EAALA,EAAOM,YACrBF,EAAc,OAALJ,QAAK,IAALA,OAAK,EAALA,EAAOI,OAChBG,EAAe,OAALP,QAAK,IAALA,OAAK,EAALA,EAAOO,QAEvB,MAAO,CAELtE,GAAI,CACFuE,QAAS,CAAC,EACVC,QAAS,CACPC,MAAO,CAAC,GAAK,IAAK,GAAK,KAAM,IAAM,GACnCC,QAAS,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,GACzBC,WAAYb,EAAa,CAAEG,aAAYE,YAEzCS,KAAM,CACJH,MAAO,CAAC,GAAK,IAAK,IAClBC,QAAS,CAAC,EAAG,EAAG,KAGpBG,KAAM,CACJN,QAAS,CAAC,EACVC,QAAS,CACPM,EAAG,CAAC,KAAM,GAAI,IAAK,EAAG,GACtBC,OAAQ,CAAC,EAAG,GAAK,IAAM,KAAO,GAC9BL,QAAS,CAAC,EAAG,EAAG,EAAG,EAAG,GACtBC,WAAUK,YAAA,GAAOlB,EAAa,CAAEG,aAAYE,aAE9CS,KAAM,CACJE,EAAG,CAAC,IAAK,GAAI,KACbC,OAAQ,CAAC,KAAO,GAAK,GACrBL,QAAS,CAAC,EAAG,EAAG,GAChBC,WAAYP,EAAY,CAAEC,cAAaC,cAG3CW,OAAQ,CACNV,QAAS,CAAC,EACVC,QAAS,CACPM,EAAG,EAAE,IAAK,IAAK,GAAI,EAAG,GACtBC,OAAQ,CAAC,EAAG,GAAK,IAAM,KAAO,GAC9BL,QAAS,CAAC,EAAG,EAAG,EAAG,EAAG,GACtBC,WAAYb,EAAa,CAAEG,aAAYE,YAEzCS,KAAM,CACJE,EAAG,EAAE,GAAI,IAAK,KACdC,OAAQ,CAAC,KAAO,GAAK,GACrBL,QAAS,CAAC,EAAG,EAAG,GAChBC,WAAYP,EAAY,CAAEC,cAAaC,cAG3CY,OAAQ,CACNX,QAAS,CAAC,EACVC,QAAS,CACPW,EAAG,EAAE,IAAK,IAAK,GAAI,EAAG,GACtBC,OAAQ,CAAC,EAAG,EAAG,IAAM,KAAO,GAC5BV,QAAS,CAAC,EAAG,EAAG,EAAG,EAAG,GACtBC,WAAYb,EAAa,CAAEG,aAAYE,YAEzCS,KAAM,CACJO,EAAG,CAAC,EAAG,IAAK,KACZC,OAAQ,CAAC,EAAG,GAAK,GACjBV,QAAS,CAAC,EAAG,EAAG,GAChBC,WAAYP,EAAY,CAAEC,cAAaC,cAG3Ce,QAAS,CACPd,QAAS,CAAC,EACVC,QAAS,CACPW,EAAG,CAAC,KAAM,GAAI,IAAK,EAAG,GACtBC,OAAQ,CAAC,EAAG,EAAG,IAAM,KAAO,GAC5BV,QAAS,CAAC,EAAG,EAAG,EAAG,EAAG,GACtBC,WAAYb,EAAa,CAAEG,aAAYE,YAEzCS,KAAM,CACJO,EAAG,CAAC,GAAI,GAAI,KACZC,OAAQ,CAAC,EAAG,GAAK,GACjBV,QAAS,CAAC,EAAG,EAAG,GAChBC,WAAYP,EAAY,CAAEC,cAAaC,cAK3CgB,IAAK,CACHd,QAAS,CAAEC,MAAO,CAAC,GAAK,IAAK,IAAMC,QAAS,CAAC,EAAG,EAAG,KAErDa,MAAO,CACLf,QAAS,CAAEM,EAAG,EAAE,GAAI,IAAK,KAAMC,OAAQ,CAAC,KAAO,GAAK,GAAIL,QAAS,CAAC,EAAG,EAAG,KAE1Ec,QAAS,CACPhB,QAAS,CAAEM,EAAG,CAAC,IAAK,GAAI,KAAMC,OAAQ,CAAC,KAAO,GAAK,GAAIL,QAAS,CAAC,EAAG,EAAG,KAEzEe,QAAS,CACPjB,QAAS,CAAEW,EAAG,CAAC,EAAG,IAAK,KAAMC,OAAQ,CAAC,EAAG,GAAK,GAAIV,QAAS,CAAC,EAAG,EAAG,KAEpEgB,SAAU,CACRlB,QAAS,CAAEW,EAAG,CAAC,GAAI,GAAI,KAAMC,OAAQ,CAAC,EAAG,GAAK,GAAIV,QAAS,CAAC,EAAG,EAAG,KAErE,ECnGUiB,EAAgB5B,IAKpB,CACLS,QAAS,CACPG,WAAY,CACViB,iBAPiB,OAAL7B,QAAK,IAALA,OAAK,EAALA,EAAO8B,YAAa,IAQhCC,eAPe,OAAL/B,QAAK,IAALA,OAAK,EAALA,EAAO8B,YAAa,MAUlCjB,KAAM,CACJD,WAAY,CACViB,iBAXkB,OAAL7B,QAAK,IAALA,OAAK,EAALA,EAAO8B,YAAa,IAYjCE,kBAAmB,M,wJCFZ,SAAStG,EAAe7C,GAAmD,IAAlD,QAAE4H,EAAO,OAAEwB,GAAS,EAAK,SAAE1G,GAAoB1C,EAAPqJ,EAAKC,YAAAtJ,EAAAuJ,GACnF,OAAIH,EAEA/G,cAAC5B,IAAG2H,wBAAA,CACFxF,UAAWI,IAAEC,IACb0E,SAAS,EACTC,QAASA,EAAU,UAAY,OAC/B1E,SAAU6F,KACNM,GAAK,IAAA3G,SAERA,KAMLL,cAAC5B,IAAG2H,wBAAA,CAACxF,UAAWI,IAAEC,IAAK0E,QAAQ,UAAUC,QAAQ,UAAUI,KAAK,OAAO9E,SAAU6F,KAAoBM,GAAK,IAAA3G,SACvGA,IAGP,C,kJC3BM8G,EAAoBC,sBAAW,CAAAzJ,EAA0C0J,KAAG,IAA5C,SAAEhH,EAAQ,KAAEsE,EAAO,UAAoBhH,EAAPqJ,EAAKC,YAAAtJ,EAAAuJ,GAAA,OACzElH,cAACsH,EAAW,CAAC3C,KAAMA,EAAKtE,SACtBL,cAACuH,IAAUxB,wBAAA,CAACpB,KAAMA,EAAM0C,IAAKA,GAASL,GAAK,IAAA3G,SACxCA,MAES,IAGhB8G,EAAkBK,UAAY,CAC5BnH,SAAUoH,IAAUC,KAAKC,WACzB/H,MAAO6H,IAAUG,MAAM,CAAC,UAAW,UAAW,UAAW,YAAa,OAAQ,UAAW,UAAW,UACpGjD,KAAM8C,IAAUG,MAAM,CAAC,QAAS,SAAU,WAG7BT,MAIf,MAAMU,EAAW,CACfC,MAAO,CAAEtC,MAAO,KAChBuC,IAAK,CAAEvC,MAAO,MAGVwC,EAAY,CAChBF,MAAO,CAAEtC,MAAO,MAChBuC,IAAK,CAAEvC,MAAO,MAGVyC,EAAW,CACfH,MAAO,CAAEtC,MAAO,MAChBuC,IAAK,CAAEvC,MAAO,MAQhB,SAAS8B,EAAWjJ,GAAsB,IAArB,KAAEsG,EAAI,SAAEtE,GAAUhC,EACrC,MAAM6J,EAAmB,UAATvD,EACVwD,EAAmB,UAATxD,EAEhB,OACE3E,cAAC5B,IAAG,CACFmC,UAAWI,IAAEC,IACbwH,SAAS,MACTC,WAAW,QACXxH,SAAWqH,GAAWL,GAAcM,GAAWF,GAAaD,EAC5D7H,GAAI,CACFtC,QAAS,eACTwC,SAEDA,GAGP,C,oJCvDMJ,EAAOmH,sBAAW,CAAAzJ,EAA2C0J,KAAG,IAA7C,SAAEhH,EAAQ,MAAEH,EAAQ,GAAE,KAAEoI,GAAgB3K,EAAPqJ,EAAKC,YAAAtJ,EAAAuJ,GAAA,OAC7DzG,eAAA8H,WAAA,CAAAlI,SAAA,CACEI,eAAC+H,IAAM,CAAAnI,SAAA,CACLL,cAAA,SAAAK,SAAQH,IACPoI,KAGHtI,cAAC5B,IAAG2H,wBAAA,CAACsB,IAAKA,GAASL,GAAK,IAAA3G,SACtBL,cAACM,IAAS,CAAAD,SACPA,SAIJ,IAGLJ,EAAKuH,UAAY,CACfnH,SAAUoH,IAAUC,KAAKC,WACzBzH,MAAOuH,IAAUgB,OACjBH,KAAMb,IAAUC,MAGHzH,K", "file": "static/js/42.b1697470.chunk.js", "sourcesContent": ["import { m } from 'framer-motion';\nimport { Link as RouterLink } from 'react-router-dom';\n// @mui\nimport { styled } from '@mui/material/styles';\nimport {\n  Box,\n  Button,\n  Typography,\n  Container,\n  Paper,\n  Avatar,\n  Grid,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n} from '@mui/material';\n// icons\nimport ExpandMoreIcon from '@mui/icons-material/ExpandMore';\n// components\nimport Page from '../components/Page';\nimport { MotionContainer, varBounce } from '../components/animate';\n\n// ----------------------------------------------------------------------\n\nconst RootStyle = styled('div')(({ theme }) => ({\n  display: 'flex',\n  minHeight: '100%',\n  alignItems: 'center',\n  paddingTop: theme.spacing(10),\n  paddingBottom: theme.spacing(5),\n}));\n\nconst SmsContainer = styled(Box)(({ theme }) => ({\n  display: 'flex',\n  flexDirection: 'column',\n  maxWidth: '100%',\n  margin: 'auto',\n  backgroundColor: theme.palette.background.paper,\n  borderRadius: theme.shape.borderRadius,\n  boxShadow: theme.shadows[2],\n  padding: theme.spacing(2),\n  minWidth: 300,\n}));\n\nconst SmsMessage = styled(Paper)(({ theme, isUser }) => ({\n  padding: theme.spacing(1),\n  borderRadius: theme.shape.borderRadius,\n  marginBottom: theme.spacing(1.5),\n  maxWidth: '80%',\n  alignSelf: isUser ? 'flex-end' : 'flex-start',\n  backgroundColor: isUser ? theme.palette.primary.main : theme.palette.grey[200],\n  color: isUser ? theme.palette.primary.contrastText : theme.palette.text.primary,\n}));\n\n// ----------------------------------------------------------------------\n\nexport default function HelpPage() {\n  // Example array of supporters (6 or more). Replace with real data.\n  const supporters = [\n    {\n      id: 1,\n      name: 'Baatarkhuu Ganbold',\n      phone: '(+976) 99199642',\n      fbLink: 'https://www.facebook.com/baatarkhuu.ganbold.94',\n      avatar: '/images/avatars/sup1.jpg',\n      locationLink: 'https://goo.gl/maps/hyvMxK7AbDwQkzWS8', // Example Google Maps link\n      locationTitle: 'Ханын материал салбар',\n    },\n    {\n      id: 2,\n      name: 'Munkh Aldar Ankhvv',\n      phone: '(+976) 99100525',\n      fbLink: 'https://www.facebook.com/monkhaldar.ankhvv',\n      avatar: '/images/avatars/sup2.jpg',\n      locationLink: 'https://goo.gl/maps/dG1AXNwAjM7LTyAN8',\n      locationTitle: 'Компюьтер ланд салбар',\n    },\n    {\n      id: 3,\n      name: 'Б.Энхтэнгэр',\n      phone: '(+976) 88136004',\n      fbLink: 'https://www.facebook.com/profile.php?id=100045189365124',\n      avatar: '/images/avatars/sup3.jpg',\n      locationLink: 'https://goo.gl/maps/vmoQPRTQ5fw8ipma7',\n      locationTitle: 'Компютер ланд',\n    },\n    {\n      id: 4,\n      name: 'Dagwadorj Batsuuri',\n      phone: '(+976) 99355473',\n      fbLink: 'https://www.facebook.com/profile.php?id=100004848316952',\n      avatar: '/images/avatars/sup4.jpg',\n      locationLink: 'https://goo.gl/maps/ZfBpguTNqqM2Yevy7',\n      locationTitle: 'Эрдэнэт салбар',\n    },\n    {\n      id: 5,\n      name: 'Ө.Орших',\n      phone: '(+976) 88156627',\n      fbLink: 'https://www.facebook.com/B.T.Zukaa',\n      avatar: '/images/avatars/sup5.jpg',\n      locationLink: 'https://goo.gl/maps/Hi1DAWFbncZzCV6K8',\n      locationTitle: 'Дархан салбар',\n    },\n    {\n      id: 6,\n      name: 'Ганзориг Нармандах',\n      phone: '(+976) 95446633',\n      fbLink: 'https://www.facebook.com/ganzorig.narmandax',\n      avatar: '/images/avatars/sup6.jpg',\n      locationLink: 'https://goo.gl/maps/4yJ4ahDkfyy5GKwt7',\n      locationTitle: 'Дорнод салбар',\n    },\n    {\n      id: 6,\n      name: 'М.Лхагваа',\n      phone: '(+976) 80895905',\n      fbLink: 'https://www.facebook.com/munkhjargl.lkhagwmndal',\n      avatar: '/images/avatars/sup7.jpg',\n      locationLink: 'https://goo.gl/maps/4yJ4ahDkfyy5GKwt7',\n      locationTitle: 'Дундговь салбар',\n    },\n  ];\n\n  // Example array of commands\n  const commands = [\n    { command: 'untar', description: 'машин унтраах үйлдэл' },\n    { command: 'as', description: 'машин асаах үйлдэл' },\n    { command: 'check', description: 'Системийн одоогийн төлөв байдалийг илтгэх JSON өгөгдлийг авах комманд.' },\n    { command: 'lock', description: \"цоожлох үйлдэл, 'locked' гэсэн хариу өгнө.\" },\n    { command: 'unlock', description: \"цоож нээх үйлдэл, 'unlocked' гэсэн хариу өгнө.\" },\n    { command: 'mirror', description: \"толь эвхэх үйлдэл, 'mirror received' гэсэн хариу өгнө.\" },\n    { command: 'update', description: 'OTA шинэчлэлтийн үйл явцыг эхлүүлж, хамгийн сүүлийн фирмварийг төхөөрөмжинд суулгана' },\n    { command: 'version', description: 'Программын одоогийн хувилбарын мэдээллийг өгнө.' },\n    { command: 'sim', description: 'Unitel SIM картын апп дээрхи мэдээллийг шинэчлэнэ' },\n    { command: 'id', description: 'Төхөөрөмжийн ID-г өгнө.' },\n    { command: 'sound off', description: 'төхөөрөмжийн дууг хаах' },\n    { command: 'sound on', description: 'төхөөрөмжийн дууг нээх' },\n    { command: 'display on', description: 'дэлгэцтэй холбох' },\n    { command: 'display off', description: 'дэлгэцээс салгах' },\n    { command: 'key on', description: 'запас түлхүүрийг байнгын идэвхтэй болгох' },\n    { command: 'key off', description: 'запас түлхүүр идэвхгүй болгох' },\n    { command: 'asa[секунд]', description: 'зasaCommand функцийг заасан хугацаанд ажиллуулна. Жишээ asa5 гэвэл 5 секундын турш машины кундакторыг түлхэнэ' },\n    { command: 'tempXXxYY', description: 'AS болон UNTAR горимуудын зорилтот температурыг тохируулна. Жишээ temp20x25 машин 20 градуст асаад 25 градуст унтрана' },\n    { command: 'asTTxx', description: 'asTT10 10 минут дараа асна' },\n    { command: 'set server1', description: 'төхөөрөмжийг 1 дүгээр серверт холбох. Бусад сервер засвартай болон сервис хийгдэх үед шилжүүлж ашиглана' },\n    { command: 'set server2', description: 'төхөөрөмжийг 2 дүгээр серверт холбох. Бусад сервер засвартай болон сервис хийгдэх үед шилжүүлж ашиглана' },\n    { command: 'set server3', description: 'төхөөрөмжийг 3 дүгээр серверт холбох. Бусад сервер засвартай болон сервис хийгдэх үед шилжүүлж ашиглана' },\n\n\n\n\n\n\n    \n    \n    \n    // ... add more commands as needed ...\n  ];\n\n  // Example array of multiple YouTube videos (5-6). Replace with real IDs/titles.\n  const tutorialVideos = [\n    { id: 1, title: 'Лиценз сунгах', embedId: 'im5wSOJ8Qv8' },\n    { id: 2, title: 'Сим карт мэдээлэл шинэчлэх www.aslaa.mn ээр орно', embedId: 'QO3PgxCIpQA' },\n    { id: 3, title: 'Хүйтэн үед буюу акк хүчдэл удаан өсөх үед аппаар машинаа асаах', embedId: 'yiOmB9mAhHY' },\n    { id: 1, title: 'Нууц үг сэргээх', embedId: '0mcA2qqlSSQ' },\n    \n  ];\n\n  // Common Q&A (FAQ) Section\n  const faqs = [\n    {\n      question: 'Машин өглөө олон комманд явуулж асаад байна заримдаа асахгүй байна яах вэ?',\n      answer: ' асаалт суулгах үед хувилсан түлхүүрийн чип буруу байршилд бэхлэгдсэн бол түлхүүрээ танихгүй асахгүй эсвэл олон даруулж асах. Үүнийг баталгаажуулхын тулд машиндаа ориг түлхүүртэйгээ ороод аппаас 1 л оролдлоор асуудалгүй асаж байвал тухайн чипээ зөв байрлалд байрлуулснаар асуудал шийдэгднэ.  машины баттерей асаах комманд илгээснээс 10 секундын дараа 13.5в оос дээш болж ассан төлөвөө илгээдэг. Өвөл хүйтэнд машин 15-20сек дараа ассан төлөвөө илгээж байгаа тул апп дээрээ дахин асаах комманд илгээхгүйгээр map button дарж төлөвөө дахин авна. Олон команд илгээх нь ассан машиныг унтраах, унтарсан машиныг асаах үйлдэл хийнэ ',\n    },\n    {\n      question: 'Миний машинд запас түлхүүр байхгүй. Танай үнэгүй түлхүүрийг ашиглаж болох уу?',\n      answer: 'Болно. Та өөрийн машины оригинал түлхүүрийг идэвхжүүлэгч болгон ашиглаж, бидний үнэгүй дагалдуулдаг түлхүүрийг хэрэглэж болно. Гэхдээ энэ тохиолдолд машины хаалганд ойртоход автоматаар нээгдэх зэрэг ухаалаг функцууд ажиллахгүй.',\n    },\n    {\n      question: 'Запас түлхүүр яагаад хэрэгтэй вэ? ',\n      answer: 'Баттерейгүй запас түлхүүрийг төхөөрөмжид холбож, машин асаах үед хэдхэн секунд идэвхждэг идэвхжүүлэгчээр ашигладаг. Зөв байрлуулснаар машин асах бөгөөд хулгайн эрсдэлгүй байдлыг хангана. Зарим ховор тохиолдолд байршил тохирохгүй бол асаах команд ирсэн ч машин асахгүй байж болох тул суулгуулсан газартаа очиж тохируулснаар хэвийн ажиллах болно. Тиймээс запас түлхүүрийг зөв газарт байрлуулах нь чухал.',\n    },\n    // Add more Q&A as needed...\n  ];\n\n  return (\n    <Page title=\"Мессеж коммандуудын жагсаалт\" sx={{ height: 1 }}>\n      <RootStyle>\n        <Container component={MotionContainer}>\n          <Box sx={{ maxWidth: 480, margin: 'auto', textAlign: 'center' }}>\n             {/* ---------- App UI Explanation Image ---------- */}\n             <m.div variants={varBounce().in}>\n              <Typography variant=\"h5\" paragraph sx={{ mb: 3 }}>\n                Апп интерфейс танилцуулга\n              </Typography>\n              <img\n                src=\"/images/appui.jpg\"\n                alt=\"App UI Explanation\"\n                style={{ width: '100%', borderRadius: '8px', marginBottom: '20px' }}\n              />\n            </m.div>\n            {/* ---------- End App UI Explanation Image ---------- */}\n            {/* ---------- Supporters Section (Responsive Grid) ---------- */}\n            <m.div variants={varBounce().in}>\n              <Typography variant=\"h5\" paragraph sx={{ mb: 3 }}>\n                Асаалт суулгалт болон суулгалтын дараах үйлчилгээ\n              </Typography>\n              <Grid container spacing={2} justifyContent=\"center\">\n                {supporters.map((supporter) => (\n                  <Grid key={supporter.id} item xs={12} sm={6} md={4}>\n                    <Box\n                      sx={{\n                        display: 'flex',\n                        flexDirection: 'column',\n                        alignItems: 'center',\n                        p: 2,\n                        border: 1,\n                        borderColor: 'grey.300',\n                        borderRadius: 2,\n                      }}\n                    >\n                      <Avatar\n                        alt={supporter.name}\n                        src={supporter.avatar}\n                        sx={{ width: 60, height: 60, mb: 1 }}\n                      />\n                      <Typography variant=\"subtitle1\" sx={{ fontWeight: 'bold' }}>\n                        {supporter.name}\n                      </Typography>\n                      <Typography variant=\"body2\" sx={{ color: 'text.secondary' }}>\n                        Утас:{' '}\n                        <a href={`tel:${supporter.phone.replace(/\\D/g, '')}`}>\n                          {supporter.phone}\n                        </a>\n                      </Typography>\n                      <Typography variant=\"body2\" sx={{ color: 'text.secondary' }}>\n                        Facebook:{' '}\n                        <a href={supporter.fbLink} target=\"_blank\" rel=\"noopener noreferrer\">\n                          {supporter.fbLink.replace(/^https?:\\/\\//, '')}\n                        </a>\n                      </Typography>\n                      {/* -------------- Location Link -------------- */}\n                      {supporter.locationLink && (\n                        <Typography variant=\"body2\" sx={{ color: 'text.secondary' }}>\n                          Хаяг:{' '}\n                          <a\n                            href={supporter.locationLink}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                          >\n                            {supporter.locationTitle || 'Map Link'}\n                          </a>\n                        </Typography>\n                      )}\n                    </Box>\n                  </Grid>\n                ))}\n              </Grid>\n            </m.div>\n            {/* ---------- End Supporters Section ---------- */}\n\n            {/* ---------- Commands List ---------- */}\n            <m.div variants={varBounce().in} sx={{ mt: 6 }}>\n              <Typography variant=\"h4\" paragraph>\n                Мессеж командуудын жагсаалт\n              </Typography>\n            </m.div>\n            <Typography sx={{ color: 'text.secondary' }}>\n              Асаалтын Системийн SMS командуудыг доор жагсаав\n            </Typography>\n\n            <m.div variants={varBounce().in}>\n              <SmsContainer sx={{ mt: 4 }}>\n                {commands.map((cmd, index) => (\n                  <div key={index}>\n                    <SmsMessage isUser>\n                      <Typography variant=\"body2\" sx={{ fontWeight: 'bold' }}>\n                        {cmd.command}\n                      </Typography>\n                    </SmsMessage>\n                    <SmsMessage>\n                      <Typography variant=\"body2\">{cmd.description}</Typography>\n                    </SmsMessage>\n                  </div>\n                ))}\n              </SmsContainer>\n            </m.div>\n            {/* ---------- End Commands List ---------- */}\n\n            {/* ---------- Multiple YouTube Videos ---------- */}\n            <m.div variants={varBounce().in}>\n              <Typography variant=\"h6\" gutterBottom sx={{ mt: 6 }}>\n                Зааварчилгаа бичлэг\n              </Typography>\n              <Grid container spacing={3} sx={{ mt: 2 }}>\n                {tutorialVideos.map((video) => (\n                  <Grid key={video.id} item xs={12}>\n                    <Typography variant=\"subtitle1\" sx={{ mb: 1 }}>\n                      {video.title}\n                    </Typography>\n                    <Box\n                      sx={{\n                        position: 'relative',\n                        paddingBottom: '56.25%', // 16:9 aspect ratio\n                        height: 0,\n                        overflow: 'hidden',\n                        borderRadius: 2,\n                        boxShadow: 1,\n                      }}\n                    >\n                      <iframe\n                        title={video.title}\n                        style={{\n                          position: 'absolute',\n                          top: 0,\n                          left: 0,\n                          width: '100%',\n                          height: '100%',\n                        }}\n                        src={`https://www.youtube.com/embed/${video.embedId}`}\n                        frameBorder=\"0\"\n                        allow=\"accelerometer; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n                        allowFullScreen\n                      />\n                    </Box>\n                  </Grid>\n                ))}\n              </Grid>\n            </m.div>\n            {/* ---------- End Multiple YouTube Videos ---------- */}\n\n            {/* ---------- Common Q&A Section ---------- */}\n            <m.div variants={varBounce().in} sx={{ mt: 6 }}>\n              <Typography variant=\"h6\" paragraph>\n                Түгээмэл асуулт &amp; Хариулт\n              </Typography>\n              {faqs.map((faq, index) => (\n                <Accordion key={index} sx={{ textAlign: 'left' }}>\n                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n                    <Typography variant=\"subtitle1\" sx={{ fontWeight: 'bold' }}>\n                      {faq.question}\n                    </Typography>\n                  </AccordionSummary>\n                  <AccordionDetails>\n                    <Typography variant=\"body2\">{faq.answer}</Typography>\n                  </AccordionDetails>\n                </Accordion>\n              ))}\n            </m.div>\n            {/* ---------- End Common Q&A Section ---------- */}\n\n            <Button\n              to=\"/home\"\n              size=\"large\"\n              variant=\"contained\"\n              component={RouterLink}\n              sx={{ mt: 5 }}\n            >\n              Go to Home\n            </Button>\n          </Box>\n        </Container>\n      </RootStyle>\n    </Page>\n  );\n}", "// ----------------------------------------------------------------------\n\nexport const TRANSITION = {\n  duration: 2,\n  ease: [0.43, 0.13, 0.23, 0.96]\n};\n\nexport const varPath = {\n  animate: {\n    fillOpacity: [0, 0, 1],\n    pathLength: [1, 0.4, 0],\n    transition: TRANSITION\n  }\n};\n", "// ----------------------------------------------------------------------\n\nexport const varTranHover = (props) => {\n  const duration = props?.duration || 0.32;\n  const ease = props?.ease || [0.43, 0.13, 0.23, 0.96];\n\n  return { duration, ease };\n};\n\nexport const varTranEnter = (props) => {\n  const duration = props?.durationIn || 0.64;\n  const ease = props?.easeIn || [0.43, 0.13, 0.23, 0.96];\n\n  return { duration, ease };\n};\n\nexport const varTranExit = (props) => {\n  const duration = props?.durationOut || 0.48;\n  const ease = props?.easeOut || [0.43, 0.13, 0.23, 0.96];\n\n  return { duration, ease };\n};\n", "import { varTranEnter, varTranExit } from './transition';\n\n// ----------------------------------------------------------------------\n\nexport const varBounce = (props) => {\n  const durationIn = props?.durationIn;\n  const durationOut = props?.durationOut;\n  const easeIn = props?.easeIn;\n  const easeOut = props?.easeOut;\n\n  return {\n    // IN\n    in: {\n      initial: {},\n      animate: {\n        scale: [0.3, 1.1, 0.9, 1.03, 0.97, 1],\n        opacity: [0, 1, 1, 1, 1, 1],\n        transition: varTranEnter({ durationIn, easeIn }),\n      },\n      exit: {\n        scale: [0.9, 1.1, 0.3],\n        opacity: [1, 1, 0],\n      },\n    },\n    inUp: {\n      initial: {},\n      animate: {\n        y: [720, -24, 12, -4, 0],\n        scaleY: [4, 0.9, 0.95, 0.985, 1],\n        opacity: [0, 1, 1, 1, 1],\n        transition: { ...varTranEnter({ durationIn, easeIn }) },\n      },\n      exit: {\n        y: [12, -24, 720],\n        scaleY: [0.985, 0.9, 3],\n        opacity: [1, 1, 0],\n        transition: varTranExit({ durationOut, easeOut }),\n      },\n    },\n    inDown: {\n      initial: {},\n      animate: {\n        y: [-720, 24, -12, 4, 0],\n        scaleY: [4, 0.9, 0.95, 0.985, 1],\n        opacity: [0, 1, 1, 1, 1],\n        transition: varTranEnter({ durationIn, easeIn }),\n      },\n      exit: {\n        y: [-12, 24, -720],\n        scaleY: [0.985, 0.9, 3],\n        opacity: [1, 1, 0],\n        transition: varTranExit({ durationOut, easeOut }),\n      },\n    },\n    inLeft: {\n      initial: {},\n      animate: {\n        x: [-720, 24, -12, 4, 0],\n        scaleX: [3, 1, 0.98, 0.995, 1],\n        opacity: [0, 1, 1, 1, 1],\n        transition: varTranEnter({ durationIn, easeIn }),\n      },\n      exit: {\n        x: [0, 24, -720],\n        scaleX: [1, 0.9, 2],\n        opacity: [1, 1, 0],\n        transition: varTranExit({ durationOut, easeOut }),\n      },\n    },\n    inRight: {\n      initial: {},\n      animate: {\n        x: [720, -24, 12, -4, 0],\n        scaleX: [3, 1, 0.98, 0.995, 1],\n        opacity: [0, 1, 1, 1, 1],\n        transition: varTranEnter({ durationIn, easeIn }),\n      },\n      exit: {\n        x: [0, -24, 720],\n        scaleX: [1, 0.9, 2],\n        opacity: [1, 1, 0],\n        transition: varTranExit({ durationOut, easeOut }),\n      },\n    },\n\n    // OUT\n    out: {\n      animate: { scale: [0.9, 1.1, 0.3], opacity: [1, 1, 0] },\n    },\n    outUp: {\n      animate: { y: [-12, 24, -720], scaleY: [0.985, 0.9, 3], opacity: [1, 1, 0] },\n    },\n    outDown: {\n      animate: { y: [12, -24, 720], scaleY: [0.985, 0.9, 3], opacity: [1, 1, 0] },\n    },\n    outLeft: {\n      animate: { x: [0, 24, -720], scaleX: [1, 0.9, 2], opacity: [1, 1, 0] },\n    },\n    outRight: {\n      animate: { x: [0, -24, 720], scaleX: [1, 0.9, 2], opacity: [1, 1, 0] },\n    },\n  };\n};\n", "// ----------------------------------------------------------------------\n\nexport const varContainer = (props) => {\n  const staggerIn = props?.staggerIn || 0.05;\n  const delayIn = props?.staggerIn || 0.05;\n  const staggerOut = props?.staggerIn || 0.05;\n\n  return {\n    animate: {\n      transition: {\n        staggerChildren: staggerIn,\n        delayChildren: delayIn\n      }\n    },\n    exit: {\n      transition: {\n        staggerChildren: staggerOut,\n        staggerDirection: -1\n      }\n    }\n  };\n};\n", "import PropTypes from 'prop-types';\nimport { m } from 'framer-motion';\n// @mui\nimport { Box } from '@mui/material';\n//\nimport { varContainer } from './variants';\n\n// ----------------------------------------------------------------------\n\nMotionContainer.propTypes = {\n  action: PropTypes.bool,\n  animate: PropTypes.bool,\n  children: PropTypes.node.isRequired\n};\n\nexport default function MotionContainer({ animate, action = false, children, ...other }) {\n  if (action) {\n    return (\n      <Box\n        component={m.div}\n        initial={false}\n        animate={animate ? 'animate' : 'exit'}\n        variants={varContainer()}\n        {...other}\n      >\n        {children}\n      </Box>\n    );\n  }\n\n  return (\n    <Box component={m.div} initial=\"initial\" animate=\"animate\" exit=\"exit\" variants={varContainer()} {...other}>\n      {children}\n    </Box>\n  );\n}\n", "import PropTypes from 'prop-types';\nimport { m } from 'framer-motion';\nimport { forwardRef } from 'react';\n// @mui\nimport { Box, IconButton } from '@mui/material';\n\n// ----------------------------------------------------------------------\n\nconst IconButtonAnimate = forwardRef(({ children, size = 'medium', ...other }, ref) => (\n  <AnimateWrap size={size}>\n    <IconButton size={size} ref={ref} {...other}>\n      {children}\n    </IconButton>\n  </AnimateWrap>\n));\n\nIconButtonAnimate.propTypes = {\n  children: PropTypes.node.isRequired,\n  color: PropTypes.oneOf(['inherit', 'default', 'primary', 'secondary', 'info', 'success', 'warning', 'error']),\n  size: PropTypes.oneOf(['small', 'medium', 'large'])\n};\n\nexport default IconButtonAnimate;\n\n// ----------------------------------------------------------------------\n\nconst varSmall = {\n  hover: { scale: 1.1 },\n  tap: { scale: 0.95 }\n};\n\nconst varMedium = {\n  hover: { scale: 1.09 },\n  tap: { scale: 0.97 }\n};\n\nconst varLarge = {\n  hover: { scale: 1.08 },\n  tap: { scale: 0.99 }\n};\n\nAnimateWrap.propTypes = {\n  children: PropTypes.node.isRequired,\n  size: PropTypes.oneOf(['small', 'medium', 'large'])\n};\n\nfunction AnimateWrap({ size, children }) {\n  const isSmall = size === 'small';\n  const isLarge = size === 'large';\n\n  return (\n    <Box\n      component={m.div}\n      whileTap=\"tap\"\n      whileHover=\"hover\"\n      variants={(isSmall && varSmall) || (isLarge && varLarge) || varMedium}\n      sx={{\n        display: 'inline-flex'\n      }}\n    >\n      {children}\n    </Box>\n  );\n}\n", "import PropTypes from 'prop-types';\nimport { Helmet } from 'react-helmet-async';\nimport { forwardRef } from 'react';\n// @mui\nimport { Box, Container } from '@mui/material';\n\n// ----------------------------------------------------------------------\n\nconst Page = forwardRef(({ children, title = '', meta, ...other }, ref) => (\n  <>\n    <Helmet>\n      <title>{title}</title>\n      {meta}\n    </Helmet>\n\n    <Box ref={ref} {...other}>\n      <Container  >\n        {children}\n      </Container>\n\n    </Box>\n  </>\n));\n\nPage.propTypes = {\n  children: PropTypes.node.isRequired,\n  title: PropTypes.string,\n  meta: PropTypes.node,\n};\n\nexport default Page;\n"], "sourceRoot": ""}