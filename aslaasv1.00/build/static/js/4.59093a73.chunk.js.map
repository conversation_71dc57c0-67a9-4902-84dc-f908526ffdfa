{"version": 3, "sources": ["../node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js", "../node_modules/@mui/material/Dialog/DialogContext.js", "../node_modules/@mui/system/esm/styled.js", "../node_modules/@mui/material/Dialog/dialogClasses.js", "../../src/observe.ts", "../../src/InView.tsx", "../../src/useInView.tsx", "../node_modules/@mui/system/esm/Container/createContainer.js", "../node_modules/@mui/material/Container/Container.js", "../node_modules/@mui/material/Typography/typographyClasses.js", "../node_modules/@mui/material/Typography/Typography.js", "../node_modules/@mui/material/IconButton/iconButtonClasses.js", "../node_modules/@mui/material/IconButton/IconButton.js", "../node_modules/@mui/material/Dialog/Dialog.js", "../node_modules/framer-motion/dist/es/render/dom/motion-minimal.js", "../node_modules/framer-motion/dist/es/components/LazyMotion/index.js", "../node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.js", "../node_modules/framer-motion/dist/es/components/AnimatePresence/index.js", "../node_modules/framer-motion/dist/es/utils/use-force-update.js", "../node_modules/framer-motion/dist/es/animation/animation-controls.js", "../node_modules/framer-motion/dist/es/animation/use-animation.js", "../node_modules/@mui/material/Fab/fabClasses.js", "../node_modules/@mui/material/Fab/Fab.js"], "names": ["_objectWithoutProperties", "e", "t", "o", "r", "i", "Object", "getOwnPropertySymbols", "n", "length", "indexOf", "propertyIsEnumerable", "call", "DialogContext", "createContext", "styled", "createStyled", "getDialogUtilityClass", "slot", "generateUtilityClass", "dialogClasses", "generateUtilityClasses", "observerMap", "Map", "RootIds", "WeakMap", "rootId", "unsupportedValue", "undefined", "optionsToId", "options", "keys", "sort", "filter", "key", "map", "root", "has", "set", "toString", "get", "observe", "element", "callback", "fallback<PERSON>n<PERSON>iew", "window", "IntersectionObserver", "bounds", "getBoundingClientRect", "isIntersecting", "target", "intersectionRatio", "threshold", "time", "boundingClientRect", "intersectionRect", "rootBounds", "_createObserver", "id", "instance", "thresholds", "elements", "observer", "entries", "for<PERSON>ach", "entry", "_elements$get", "inView", "some", "trackVisibility", "isVisible", "Array", "isArray", "createObserver", "callbacks", "push", "splice", "unobserve", "size", "disconnect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props", "children", "InView", "_React$Component", "_this", "node", "_unobserveCb", "handleNode", "triggerOnce", "skip", "setState", "initialInView", "observeNode", "handleChange", "onChange", "state", "_proto", "prototype", "componentDidUpdate", "prevProps", "rootMargin", "this", "delay", "componentWillUnmount", "_this$props", "render", "_this$state", "ref", "_this$props2", "as", "_objectWithoutPropertiesLoose", "_excluded", "React", "_extends", "useInView", "_temp", "_ref", "_React$useState", "setRef", "current", "useEffect", "result", "displayName", "defaultProps", "defaultTheme", "createTheme", "defaultCreateStyledComponent", "systemStyled", "name", "overridesResolver", "styles", "ownerState", "concat", "capitalize", "String", "max<PERSON><PERSON><PERSON>", "fixed", "disableGutters", "useThemePropsDefault", "inProps", "useThemePropsSystem", "useUtilityClasses", "componentName", "classes", "slots", "composeClasses", "Container", "arguments", "createStyledComponent", "useThemeProps", "ContainerRoot", "theme", "width", "marginLeft", "boxSizing", "marginRight", "display", "paddingLeft", "spacing", "paddingRight", "breakpoints", "up", "_ref2", "values", "reduce", "acc", "breakpoint<PERSON><PERSON><PERSON><PERSON><PERSON>", "breakpoint", "value", "unit", "_ref3", "Math", "max", "xs", "className", "component", "other", "_jsx", "clsx", "createContainer", "getTypographyUtilityClass", "typographyClasses", "TypographyRoot", "variant", "align", "noWrap", "gutterBottom", "paragraph", "margin", "typography", "textAlign", "overflow", "textOverflow", "whiteSpace", "marginBottom", "defaultVariantMapping", "h1", "h2", "h3", "h4", "h5", "h6", "subtitle1", "subtitle2", "body1", "body2", "inherit", "colorTransformations", "primary", "textPrimary", "secondary", "textSecondary", "error", "Typography", "themeProps", "color", "transformDeprecatedColors", "extendSxProp", "variantMapping", "Component", "getIconButtonUtilityClass", "iconButtonClasses", "IconButtonRoot", "ButtonBase", "edge", "flex", "fontSize", "pxToRem", "padding", "borderRadius", "vars", "palette", "action", "active", "transition", "transitions", "create", "duration", "shortest", "disable<PERSON><PERSON><PERSON>", "backgroundColor", "activeChannel", "hoverOpacity", "alpha", "_palette", "main", "mainChannel", "disabled", "IconButton", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "centerRipple", "focusRipple", "DialogBackdrop", "Backdrop", "overrides", "backdrop", "zIndex", "DialogRoot", "Modal", "position", "DialogContainer", "container", "scroll", "height", "outline", "justifyContent", "alignItems", "overflowY", "overflowX", "content", "verticalAlign", "DialogPaper", "Paper", "paper", "fullWidth", "paperFullWidth", "fullScreen", "paperFullScreen", "boxShadow", "flexDirection", "maxHeight", "paperScrollBody", "down", "Dialog", "useTheme", "defaultTransitionDuration", "enter", "enteringScreen", "exit", "leavingScreen", "aria<PERSON><PERSON><PERSON><PERSON>", "ariaLabelledbyProp", "BackdropComponent", "BackdropProps", "disableEscapeKeyDown", "onBackdropClick", "onClose", "open", "PaperComponent", "PaperProps", "TransitionComponent", "Fade", "transitionDuration", "TransitionProps", "backdropClick", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useId", "dialogContextValue", "titleId", "closeAfterTransition", "components", "componentsProps", "onClick", "event", "appear", "in", "timeout", "role", "onMouseDown", "currentTarget", "elevation", "Provider", "m", "createMotionProxy", "createDomMotionConfig", "LazyMotion", "_a", "features", "_b", "strict", "setIsLoaded", "__read", "useState", "isLazyBundle", "<PERSON><PERSON><PERSON><PERSON>", "useRef", "renderer", "loadedFeatures", "__rest", "loadFeatures", "then", "LazyContext", "presenceId", "getPresenceId", "Presence<PERSON><PERSON><PERSON>", "initial", "isPresent", "onExitComplete", "custom", "presenceAffectsLayout", "presenceC<PERSON><PERSON>n", "useConstant", "newChildrenMap", "context", "useMemo", "childId", "allComplete", "isComplete", "register", "delete", "_", "PresenceContext", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "child", "AnimatePresence", "exitBeforeEnter", "_c", "forceRender", "unloadingRef", "forcedRenderCount", "setForcedRenderCount", "useUnmountEffect", "useCallback", "useForceUpdate", "layoutContext", "useContext", "SharedLayoutContext", "isSharedLayout", "forceUpdate", "isInitialRender", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filtered", "Children", "isValidElement", "onlyElements", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "allChildren", "exiting", "Set", "updateChildLookup", "children<PERSON><PERSON><PERSON><PERSON>", "__spread<PERSON><PERSON>y", "present<PERSON><PERSON>s", "targetKeys", "numPresent", "add", "insertionIndex", "removeIndex", "findIndex", "<PERSON><PERSON><PERSON><PERSON>", "cloneElement", "animationControls", "hasMounted", "pendingAnimations", "subscribers", "controls", "subscribe", "visualElement", "start", "definition", "transitionOverride", "animations_1", "animateVisualElement", "Promise", "all", "resolve", "animation", "invariant", "set<PERSON><PERSON><PERSON>", "stop", "stopAnimation", "mount", "apply", "useAnimation", "getFabUtilityClass", "fabClasses", "FabRoot", "shouldForwardProp", "prop", "rootShouldForwardProp", "colorInherit", "_theme$palette$getCon", "_theme$palette", "button", "minHeight", "short", "min<PERSON><PERSON><PERSON>", "fab", "shadows", "text", "getContrastText", "grey", "A100", "textDecoration", "focusVisible", "contrastText", "dark", "disabledBackground", "Fab", "focusVisibleClassName", "composedClasses"], "mappings": "kGAAA,8CACA,SAASA,EAAyBC,EAAGC,GACnC,GAAI,MAAQD,EAAG,MAAO,CAAC,EACvB,IAAIE,EACFC,EACAC,EAAI,YAA6BJ,EAAGC,GACtC,GAAII,OAAOC,sBAAuB,CAChC,IAAIC,EAAIF,OAAOC,sBAAsBN,GACrC,IAAKG,EAAI,EAAGA,EAAII,EAAEC,OAAQL,IAAKD,EAAIK,EAAEJ,IAAK,IAAMF,EAAEQ,QAAQP,IAAM,CAAC,EAAEQ,qBAAqBC,KAAKX,EAAGE,KAAOE,EAAEF,GAAKF,EAAEE,GAClH,CACA,OAAOE,CACT,C,mCCXA,WACA,MAAMQ,EAA6BC,wBAAc,CAAC,GAInCD,K,mCCLf,aACA,MAAME,EAASC,cACAD,K,mCCFf,wDAEO,SAASE,EAAsBC,GACpC,OAAOC,YAAqB,YAAaD,EAC3C,CACA,MAAME,EAAgBC,YAAuB,YAAa,CAAC,OAAQ,cAAe,aAAc,YAAa,QAAS,mBAAoB,kBAAmB,kBAAmB,eAAgB,eAAgB,eAAgB,eAAgB,eAAgB,iBAAkB,oBACnQD,K,6XCJf,IAAME,EAAc,IAAIC,IASlBC,EAA+C,IAAIC,QACrDC,EAAS,EAETC,OAAwCC,EA6BtC,SAAUC,EAAYC,GACnB,OAAAxB,OAAOyB,KAAKD,GAChBE,OACAC,QAAO,SAACC,GAAD,YAA0BN,IAAjBE,EAAQI,EAAjB,IACPC,KAAI,SAACD,GACJ,OAAUA,EACR,KAAQ,SAARA,GAnBWE,EAmBgBN,EAAQM,OAjBrCZ,EAAQa,IAAID,KAChBV,GAAU,EACVF,EAAQc,IAAIF,EAAMV,EAAOa,aAFKf,EAAQgB,IAAIJ,IADxB,IAkB+BN,EAAQI,IAnB3D,IAAmBE,CAqBd,IACAG,UACJ,CA2De,SAAAE,EACdC,EACAC,EACAb,EACAc,GAGE,QAH+B,IADjCd,MAAoC,CAAC,QACJ,IAAjCc,MAAiBjB,GAGwB,qBAAhCkB,OAAOC,2BACKlB,IAAnBgB,EACA,CACA,IAAMG,EAASL,EAAQM,wBAWvB,OAVAL,EAASC,EAAgB,CACvBK,eAAgBL,EAChBM,OAAQR,EACRS,kBAC+B,kBAAtBrB,EAAQsB,UAAyBtB,EAAQsB,UAAY,EAC9DC,KAAM,EACNC,mBAAoBP,EACpBQ,iBAAkBR,EAClBS,WAAYT,IAEP,WAAK,CAGb,CAEkC,IAAAU,EAnFrC,SAAwB3B,GAEtB,IAAI4B,EAAK7B,EAAYC,GACjB6B,EAAWrC,EAAYkB,IAAIkB,GAE3B,IAACC,EAAU,CAEb,IACIC,EADEC,EAAW,IAAItC,IAGfuC,EAAW,IAAIhB,sBAAqB,SAACiB,GACzCA,EAAQC,SAAQ,SAACC,GAAS,IAAAC,EAGlBC,EACJF,EAAMhB,gBACNW,EAAWQ,MAAK,SAAChB,GAAD,OAAea,EAAMd,mBAAqBC,CAA1C,IAGdtB,EAAQuC,iBAA8C,qBAApBJ,EAAMK,YAG1CL,EAAMK,UAAYH,GAGpB,OAAAD,EAAAL,EAASrB,IAAIyB,EAAMf,UAAnBgB,EAA4BF,SAAQ,SAACrB,GACnCA,EAASwB,EAAQF,EAClB,GACF,GACF,GAAEnC,GAGH8B,EACEE,EAASF,aACRW,MAAMC,QAAQ1C,EAAQsB,WACnBtB,EAAQsB,UACR,CAACtB,EAAQsB,WAAa,IAE5BO,EAAW,CACTD,KACAI,WACAD,YAGFvC,EAAYgB,IAAIoB,EAAIC,EACrB,CAED,OAAOA,CACR,CAmCoCc,CAAe3C,GAA1C4B,EAARD,EAAQC,GAAII,EAAZL,EAAYK,SAAUD,EAAtBJ,EAAsBI,SAGlBa,EAAYb,EAASrB,IAAIE,IAAY,GAQlC,OAPFmB,EAASxB,IAAIK,IAChBmB,EAASvB,IAAII,EAASgC,GAGxBA,EAAUC,KAAKhC,GACfmB,EAASrB,QAAQC,GAEV,WAELgC,EAAUE,OAAOF,EAAUhE,QAAQiC,GAAW,GAErB,IAArB+B,EAAUjE,SAEZoD,EAAA,OAAgBnB,GAChBoB,EAASe,UAAUnC,IAGC,IAAlBmB,EAASiB,OAEXhB,EAASiB,aACTzD,EAAA,OAAmBoC,GAEtB,CACF,C,mJC5JD,SAASsB,EACPC,GAEA,MAAiC,oBAAnBA,EAAMC,QACrB,CAmDD,IAAaC,EAAb,SAAAC,G,QAWE,SAAAD,EAAYF,GAAqD,IAAAI,EAAA,OAC/DA,EAAAD,EAAAxE,KAAA,KAAMqE,IAAN,MA2BFK,KAAuB,KACvBD,EAAAE,aAAoC,KA7B6BF,EAiEjEG,WAAa,SAACF,GACRD,EAAKC,OAEPD,EAAKR,YAEAS,GAASD,EAAKJ,MAAMQ,aAAgBJ,EAAKJ,MAAMS,MAElDL,EAAKM,SAAS,CAAExB,SAAUkB,EAAKJ,MAAMW,cAAe3B,WAAOrC,KAI/DyD,EAAKC,KAAOA,GAAc,KAC1BD,EAAKQ,aACN,EA9EgER,EAgFjES,aAAe,SAAC3B,EAAiBF,GAC3BE,GAAUkB,EAAKJ,MAAMQ,aAEvBJ,EAAKR,YAEFG,EAAgBK,EAAKJ,QAGxBI,EAAKM,SAAS,CAAExB,SAAQF,UAEtBoB,EAAKJ,MAAMc,UAEbV,EAAKJ,MAAMc,SAAS5B,EAAQF,EAE/B,EA5FCoB,EAAKW,MAAQ,CACX7B,SAAUc,EAAMW,cAChB3B,WAAOrC,GAJsDyD,CAMhE,C,EAjBHD,G,EAAAD,G,sEAAA,IAAAc,EAAAd,EAAAe,UAAA,OAAAD,EAmBEE,mBAAA,SAAmBC,GAGfA,EAAUC,aAAeC,KAAKrB,MAAMoB,YACpCD,EAAUhE,OAASkE,KAAKrB,MAAM7C,MAC9BgE,EAAUhD,YAAckD,KAAKrB,MAAM7B,WACnCgD,EAAUV,OAASY,KAAKrB,MAAMS,MAC9BU,EAAU/B,kBAAoBiC,KAAKrB,MAAMZ,iBACzC+B,EAAUG,QAAUD,KAAKrB,MAAMsB,QAE/BD,KAAKzB,YACLyB,KAAKT,cAER,EAEDI,EAAAO,qBAAA,WACEF,KAAKzB,YACA,KAAAS,KAAO,IACb,EAKDW,EAAAJ,YAAA,WACM,GAACS,KAAKhB,OAAQgB,KAAKrB,MAAMS,KAAzB,CACJ,IAAAe,EAOIH,KAAKrB,MANP7B,EADFqD,EACErD,UACAhB,EAFFqE,EAEErE,KACAiE,EAHFI,EAGEJ,WACAhC,EAJFoC,EAIEpC,gBACAkC,EALFE,EAKEF,MACA3D,EANF6D,EAME7D,eAGG,KAAA2C,aAAe9C,EAClB6D,KAAKhB,KACLgB,KAAKR,aACL,CACE1C,YACAhB,OACAiE,aAEAhC,kBAEAkC,SAEF3D,EAtBuC,CAwB1C,EAEDqD,EAAApB,UAAA,WACMyB,KAAKf,eACPe,KAAKf,eACA,KAAAA,aAAe,KAEvB,EAiCDU,EAAAS,OAAA,WACE,IAAK1B,EAAgBsB,KAAKrB,OAAQ,CAChC,IAAA0B,EAA0BL,KAAKN,MAAvB7B,EAARwC,EAAQxC,OAAQF,EAAhB0C,EAAgB1C,MAChB,OAAOqC,KAAKrB,MAAMC,SAAS,CAAEf,SAAQF,QAAO2C,IAAKN,KAAKd,YACvD,CAED,IAAAqB,EAcIP,KAAKrB,MAbPC,EADF2B,EACE3B,SACA4B,EAFFD,EAEEC,GAWG7B,E,oIAbL8B,CAAAF,EAAAG,GAgBA,OAAOC,gBACLH,GAAM,MADDI,EAAA,CAEHN,IAAKN,KAAKd,YAAeP,GAC3BC,EAEH,EAtIHC,CAAA,EAA4B8B,aC5BtB,SAAUE,EAUWC,GAAA,IAAAC,OAAA,IAAAD,EAAF,CAAC,EAACA,EATzBhE,EASyBiE,EATzBjE,UACAmD,EAQyBc,EARzBd,MACAlC,EAOyBgD,EAPzBhD,gBACAgC,EAMyBgB,EANzBhB,WACAjE,EAKyBiF,EALzBjF,KACAqD,EAIyB4B,EAJzB5B,YACAC,EAGyB2B,EAHzB3B,KACAE,EAEyByB,EAFzBzB,cACAhD,EACyByE,EADzBzE,eAEMiC,EAAYoC,WACQK,EAAAL,WAAsB,CAC9C9C,SAAUyB,IADLI,EAAPsB,EAAA,GAAc3B,EAAd2B,EAAA,GAGMC,EAASN,eACb,SAAC3B,QAC2B1D,IAAtBiD,EAAU2C,UACZ3C,EAAU2C,UACV3C,EAAU2C,aAAU5F,GAIlB8D,GAEAJ,IACFT,EAAU2C,QAAU/E,EAClB6C,GACA,SAACnB,EAAQF,GACP0B,EAAS,CAAExB,SAAQF,UAEfA,EAAMhB,gBAAkBwC,GAAeZ,EAAU2C,UAEnD3C,EAAU2C,UACV3C,EAAU2C,aAAU5F,EAEvB,GACD,CACEQ,OACAiE,aACAjD,YAEAiB,kBAEAkC,SAEF3D,GAGL,GAIC,CAEA2B,MAAMC,QAAQpB,GAAaA,EAAUb,WAAaa,EAClDhB,EACAiE,EACAZ,EACAC,EACArB,EACAzB,EACA2D,IAKJkB,qBAAU,WACH5C,EAAU2C,UAAWxB,EAAM/B,OAAUwB,GAAgBC,GAGxDC,EAAS,CACPxB,SAAUyB,GAGf,IAED,IAAM8B,EAAS,CAACH,EAAQvB,EAAM7B,OAAQ6B,EAAM/B,OAO5C,OAJAyD,EAAOd,IAAMc,EAAO,GACpBA,EAAOvD,OAASuD,EAAO,GACvBA,EAAOzD,MAAQyD,EAAO,GAEfA,CACR,CDzDYvC,EAIJwC,YAAc,SAJVxC,EAKJyC,aAAe,CACpBxE,UAAW,EACXqC,aAAa,EACbG,eAAe,E,mIEtEnB,MAAMoB,EAAY,CAAC,YAAa,YAAa,iBAAkB,QAAS,WAAY,WAW9Ea,EAAeC,cACfC,EAA+BC,YAAa,MAAO,CACvDC,KAAM,eACN/G,KAAM,OACNgH,kBAAmBA,CAACjD,EAAOkD,KACzB,MAAM,WACJC,GACEnD,EACJ,MAAO,CAACkD,EAAO/F,KAAM+F,EAAO,WAADE,OAAYC,YAAWC,OAAOH,EAAWI,aAAeJ,EAAWK,OAASN,EAAOM,MAAOL,EAAWM,gBAAkBP,EAAOO,eAAe,IAGtKC,EAAuBC,GAAWC,YAAoB,CAC1D5D,MAAO2D,EACPX,KAAM,eACNJ,iBAEIiB,EAAoBA,CAACV,EAAYW,KACrC,MAGM,QACJC,EAAO,MACPP,EAAK,eACLC,EAAc,SACdF,GACEJ,EACEa,EAAQ,CACZ7G,KAAM,CAAC,OAAQoG,GAAY,WAAJH,OAAeC,YAAWC,OAAOC,KAAcC,GAAS,QAASC,GAAkB,mBAE5G,OAAOQ,YAAeD,GAZW/H,GACxBC,YAAqB4H,EAAe7H,IAWU8H,EAAQ,E,4BCpCjE,MAAMG,EDsCS,WAAuC,IAAdrH,EAAOsH,UAAA3I,OAAA,QAAAmB,IAAAwH,UAAA,GAAAA,UAAA,GAAG,CAAC,EACjD,MAAM,sBAEJC,EAAwBtB,EAA4B,cACpDuB,EAAgBX,EAAoB,cACpCI,EAAgB,gBACdjH,EACEyH,EAAgBF,GAAsBhC,IAAA,IAAC,MAC3CmC,EAAK,WACLpB,GACDf,EAAA,OAAKH,YAAS,CACbuC,MAAO,OACPC,WAAY,OACZC,UAAW,aACXC,YAAa,OACbC,QAAS,UACPzB,EAAWM,gBAAkB,CAC/BoB,YAAaN,EAAMO,QAAQ,GAC3BC,aAAcR,EAAMO,QAAQ,GAE5B,CAACP,EAAMS,YAAYC,GAAG,OAAQ,CAC5BJ,YAAaN,EAAMO,QAAQ,GAC3BC,aAAcR,EAAMO,QAAQ,KAE9B,IAAEI,IAAA,IAAC,MACHX,EAAK,WACLpB,GACD+B,EAAA,OAAK/B,EAAWK,OAASnI,OAAOyB,KAAKyH,EAAMS,YAAYG,QAAQC,QAAO,CAACC,EAAKC,KAC3E,MAAMC,EAAaD,EACbE,EAAQjB,EAAMS,YAAYG,OAAOI,GAOvC,OANc,IAAVC,IAEFH,EAAId,EAAMS,YAAYC,GAAGM,IAAe,CACtChC,SAAU,GAAFH,OAAKoC,GAAKpC,OAAGmB,EAAMS,YAAYS,QAGpCJ,CAAG,GACT,CAAC,EAAE,IAAEK,IAAA,IAAC,MACPnB,EAAK,WACLpB,GACDuC,EAAA,OAAKzD,YAAS,CAAC,EAA2B,OAAxBkB,EAAWI,UAAqB,CAEjD,CAACgB,EAAMS,YAAYC,GAAG,OAAQ,CAE5B1B,SAAUoC,KAAKC,IAAIrB,EAAMS,YAAYG,OAAOU,GAAI,OAEjD1C,EAAWI,UAEU,OAAxBJ,EAAWI,UAAqB,CAE9B,CAACgB,EAAMS,YAAYC,GAAG9B,EAAWI,WAAY,CAE3CA,SAAU,GAAFH,OAAKmB,EAAMS,YAAYG,OAAOhC,EAAWI,WAASH,OAAGmB,EAAMS,YAAYS,QAEjF,IACIvB,EAAyBlC,cAAiB,SAAmB2B,EAAShC,GAC1E,MAAM3B,EAAQqE,EAAcV,IACtB,UACFmC,EAAS,UACTC,EAAY,MAAK,eACjBtC,GAAiB,EAAK,MACtBD,GAAQ,EAAK,SACbD,EAAW,MACTvD,EACJgG,EAAQlE,YAA8B9B,EAAO+B,GACzCoB,EAAalB,YAAS,CAAC,EAAGjC,EAAO,CACrC+F,YACAtC,iBACAD,QACAD,aAIIQ,EAAUF,EAAkBV,EAAYW,GAC9C,OAGEmC,aAHK,CAGA3B,EAAerC,YAAS,CAC3BJ,GAAIkE,EAGJ5C,WAAYA,EACZ2C,UAAWI,YAAKnC,EAAQ5G,KAAM2I,GAC9BnE,IAAKA,GACJqE,GAEP,IAWA,OAAO9B,CACT,CCxIkBiC,CAAgB,CAChC/B,sBAAuBtI,YAAO,MAAO,CACnCkH,KAAM,eACN/G,KAAM,OACNgH,kBAAmBA,CAACjD,EAAOkD,KACzB,MAAM,WACJC,GACEnD,EACJ,MAAO,CAACkD,EAAO/F,KAAM+F,EAAO,WAADE,OAAYC,YAAWC,OAAOH,EAAWI,aAAeJ,EAAWK,OAASN,EAAOM,MAAOL,EAAWM,gBAAkBP,EAAOO,eAAe,IAG5KY,cAAeV,GAAWU,YAAc,CACtCrE,MAAO2D,EACPX,KAAM,mBA8CKkB,K,iIC/DR,SAASkC,EAA0BnK,GACxC,OAAOC,YAAqB,gBAAiBD,EAC/C,CAC0BG,YAAuB,gBAAiB,CAAC,OAAQ,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,YAAa,YAAa,QAAS,QAAS,UAAW,SAAU,UAAW,WAAY,YAAa,aAAc,cAAe,eAAgB,SAAU,eAAgB,cAC5QiK,I,OCJf,MAAMtE,EAAY,CAAC,QAAS,YAAa,YAAa,eAAgB,SAAU,YAAa,UAAW,kBAyB3FuE,EAAiBxK,YAAO,OAAQ,CAC3CkH,KAAM,gBACN/G,KAAM,OACNgH,kBAAmBA,CAACjD,EAAOkD,KACzB,MAAM,WACJC,GACEnD,EACJ,MAAO,CAACkD,EAAO/F,KAAMgG,EAAWoD,SAAWrD,EAAOC,EAAWoD,SAA+B,YAArBpD,EAAWqD,OAAuBtD,EAAO,QAADE,OAASC,YAAWF,EAAWqD,SAAWrD,EAAWsD,QAAUvD,EAAOuD,OAAQtD,EAAWuD,cAAgBxD,EAAOwD,aAAcvD,EAAWwD,WAAazD,EAAOyD,UAAU,GAP5P7K,EAS3BsG,IAAA,IAAC,MACFmC,EAAK,WACLpB,GACDf,EAAA,OAAKH,YAAS,CACb2E,OAAQ,GACPzD,EAAWoD,SAAWhC,EAAMsC,WAAW1D,EAAWoD,SAA+B,YAArBpD,EAAWqD,OAAuB,CAC/FM,UAAW3D,EAAWqD,OACrBrD,EAAWsD,QAAU,CACtBM,SAAU,SACVC,aAAc,WACdC,WAAY,UACX9D,EAAWuD,cAAgB,CAC5BQ,aAAc,UACb/D,EAAWwD,WAAa,CACzBO,aAAc,IACd,IACIC,EAAwB,CAC5BC,GAAI,KACJC,GAAI,KACJC,GAAI,KACJC,GAAI,KACJC,GAAI,KACJC,GAAI,KACJC,UAAW,KACXC,UAAW,KACXC,MAAO,IACPC,MAAO,IACPC,QAAS,KAILC,EAAuB,CAC3BC,QAAS,eACTC,YAAa,eACbC,UAAW,iBACXC,cAAe,iBACfC,MAAO,cAKHC,EAA0BrG,cAAiB,SAAoB2B,EAAShC,GAC5E,MAAM2G,EAAajE,YAAc,CAC/BrE,MAAO2D,EACPX,KAAM,kBAEFuF,EAR0BA,IACzBR,EAAqBQ,IAAUA,EAOxBC,CAA0BF,EAAWC,OAC7CvI,EAAQyI,YAAaxG,YAAS,CAAC,EAAGqG,EAAY,CAClDC,YAEI,MACF/B,EAAQ,UAAS,UACjBV,EAAS,UACTC,EAAS,aACTW,GAAe,EAAK,OACpBD,GAAS,EAAK,UACdE,GAAY,EAAK,QACjBJ,EAAU,QAAO,eACjBmC,EAAiBvB,GACfnH,EACJgG,EAAQlE,YAA8B9B,EAAO+B,GACzCoB,EAAalB,YAAS,CAAC,EAAGjC,EAAO,CACrCwG,QACA+B,QACAzC,YACAC,YACAW,eACAD,SACAE,YACAJ,UACAmC,mBAEIC,EAAY5C,IAAcY,EAAY,IAAM+B,EAAenC,IAAYY,EAAsBZ,KAAa,OAC1GxC,EAhGkBZ,KACxB,MAAM,MACJqD,EAAK,aACLE,EAAY,OACZD,EAAM,UACNE,EAAS,QACTJ,EAAO,QACPxC,GACEZ,EACEa,EAAQ,CACZ7G,KAAM,CAAC,OAAQoJ,EAA8B,YAArBpD,EAAWqD,OAAuB,QAAJpD,OAAYC,YAAWmD,IAAUE,GAAgB,eAAgBD,GAAU,SAAUE,GAAa,cAE1J,OAAO1C,YAAeD,EAAOoC,EAA2BrC,EAAQ,EAoFhDF,CAAkBV,GAClC,OAAoB8C,cAAKK,EAAgBrE,YAAS,CAChDJ,GAAI8G,EACJhH,IAAKA,EACLwB,WAAYA,EACZ2C,UAAWI,YAAKnC,EAAQ5G,KAAM2I,IAC7BE,GACL,IA4EeqC,K,2IC9LR,SAASO,EAA0B3M,GACxC,OAAOC,YAAqB,gBAAiBD,EAC/C,CAEe4M,MADWzM,YAAuB,gBAAiB,CAAC,OAAQ,WAAY,eAAgB,eAAgB,iBAAkB,aAAc,YAAa,eAAgB,eAAgB,YAAa,UAAW,YAAa,aAAc,c,OCHvP,MAAM2F,EAAY,CAAC,OAAQ,WAAY,YAAa,QAAS,WAAY,qBAAsB,QA0BzF+G,EAAiBhN,YAAOiN,IAAY,CACxC/F,KAAM,gBACN/G,KAAM,OACNgH,kBAAmBA,CAACjD,EAAOkD,KACzB,MAAM,WACJC,GACEnD,EACJ,MAAO,CAACkD,EAAO/F,KAA2B,YAArBgG,EAAWoF,OAAuBrF,EAAO,QAADE,OAASC,YAAWF,EAAWoF,SAAWpF,EAAW6F,MAAQ9F,EAAO,OAADE,OAAQC,YAAWF,EAAW6F,QAAU9F,EAAO,OAADE,OAAQC,YAAWF,EAAWtD,QAAS,GAPlM/D,EASpBsG,IAAA,IAAC,MACFmC,EAAK,WACLpB,GACDf,EAAA,OAAKH,YAAS,CACb6E,UAAW,SACXmC,KAAM,WACNC,SAAU3E,EAAMsC,WAAWsC,QAAQ,IACnCC,QAAS,EACTC,aAAc,MACdtC,SAAU,UAEVwB,OAAQhE,EAAM+E,MAAQ/E,GAAOgF,QAAQC,OAAOC,OAC5CC,WAAYnF,EAAMoF,YAAYC,OAAO,mBAAoB,CACvDC,SAAUtF,EAAMoF,YAAYE,SAASC,aAErC3G,EAAW4G,eAAiB,CAC9B,UAAW,CACTC,gBAAiBzF,EAAM+E,KAAO,QAAHlG,OAAWmB,EAAM+E,KAAKC,QAAQC,OAAOS,cAAa,OAAA7G,OAAMmB,EAAM+E,KAAKC,QAAQC,OAAOU,aAAY,KAAMC,YAAM5F,EAAMgF,QAAQC,OAAOC,OAAQlF,EAAMgF,QAAQC,OAAOU,cAEvL,uBAAwB,CACtBF,gBAAiB,iBAGA,UAApB7G,EAAW6F,MAAoB,CAChCvE,WAAgC,UAApBtB,EAAWtD,MAAoB,GAAK,IAC3B,QAApBsD,EAAW6F,MAAkB,CAC9BrE,YAAiC,UAApBxB,EAAWtD,MAAoB,GAAK,IACjD,IAAEqF,IAGE,IAHD,MACHX,EAAK,WACLpB,GACD+B,EACC,IAAIkF,EACJ,MAAMb,EAAwD,OAA7Ca,GAAY7F,EAAM+E,MAAQ/E,GAAOgF,cAAmB,EAASa,EAASjH,EAAWoF,OAClG,OAAOtG,YAAS,CAAC,EAAwB,YAArBkB,EAAWoF,OAAuB,CACpDA,MAAO,WACe,YAArBpF,EAAWoF,OAA4C,YAArBpF,EAAWoF,OAAuBtG,YAAS,CAC9EsG,MAAkB,MAAXgB,OAAkB,EAASA,EAAQc,OACxClH,EAAW4G,eAAiB,CAC9B,UAAW9H,YAAS,CAAC,EAAGsH,GAAW,CACjCS,gBAAiBzF,EAAM+E,KAAO,QAAHlG,OAAWmG,EAAQe,YAAW,OAAAlH,OAAMmB,EAAM+E,KAAKC,QAAQC,OAAOU,aAAY,KAAMC,YAAMZ,EAAQc,KAAM9F,EAAMgF,QAAQC,OAAOU,eACnJ,CAED,uBAAwB,CACtBF,gBAAiB,mBAGC,UAApB7G,EAAWtD,MAAoB,CACjCuJ,QAAS,EACTF,SAAU3E,EAAMsC,WAAWsC,QAAQ,KACd,UAApBhG,EAAWtD,MAAoB,CAChCuJ,QAAS,GACTF,SAAU3E,EAAMsC,WAAWsC,QAAQ,KAClC,CACD,CAAC,KAAD/F,OAAMyF,EAAkB0B,WAAa,CACnCP,gBAAiB,cACjBzB,OAAQhE,EAAM+E,MAAQ/E,GAAOgF,QAAQC,OAAOe,WAE9C,IAOEC,EAA0BxI,cAAiB,SAAoB2B,EAAShC,GAC5E,MAAM3B,EAAQqE,YAAc,CAC1BrE,MAAO2D,EACPX,KAAM,mBAEF,KACFgG,GAAO,EAAK,SACZ/I,EAAQ,UACR6F,EAAS,MACTyC,EAAQ,UAAS,SACjBgC,GAAW,EAAK,mBAChBE,GAAqB,EAAK,KAC1B5K,EAAO,UACLG,EACJgG,EAAQlE,YAA8B9B,EAAO+B,GACzCoB,EAAalB,YAAS,CAAC,EAAGjC,EAAO,CACrCgJ,OACAT,QACAgC,WACAE,qBACA5K,SAEIkE,EA5GkBZ,KACxB,MAAM,QACJY,EAAO,SACPwG,EAAQ,MACRhC,EAAK,KACLS,EAAI,KACJnJ,GACEsD,EACEa,EAAQ,CACZ7G,KAAM,CAAC,OAAQoN,GAAY,WAAsB,YAAVhC,GAAuB,QAAJnF,OAAYC,YAAWkF,IAAUS,GAAQ,OAAJ5F,OAAWC,YAAW2F,IAAS,OAAF5F,OAASC,YAAWxD,MAElJ,OAAOoE,YAAeD,EAAO4E,EAA2B7E,EAAQ,EAiGhDF,CAAkBV,GAClC,OAAoB8C,cAAK6C,EAAgB7G,YAAS,CAChD6D,UAAWI,YAAKnC,EAAQ5G,KAAM2I,GAC9B4E,cAAc,EACdC,aAAcF,EACdF,SAAUA,EACV5I,IAAKA,EACLwB,WAAYA,GACX6C,EAAO,CACR/F,SAAUA,IAEd,IAoEeuK,K,mCC1Mf,sJAEA,MAAMzI,EAAY,CAAC,mBAAoB,kBAAmB,oBAAqB,gBAAiB,WAAY,YAAa,uBAAwB,aAAc,YAAa,WAAY,kBAAmB,UAAW,OAAQ,iBAAkB,aAAc,SAAU,sBAAuB,qBAAsB,mBAiB/S6I,EAAiB9O,YAAO+O,IAAU,CACtC7H,KAAM,YACN/G,KAAM,WACN6O,UAAWA,CAAC9K,EAAOkD,IAAWA,EAAO6H,UAHhBjP,CAIpB,CAEDkP,QAAS,IAiBLC,EAAanP,YAAOoP,IAAO,CAC/BlI,KAAM,YACN/G,KAAM,OACNgH,kBAAmBA,CAACjD,EAAOkD,IAAWA,EAAO/F,MAH5BrB,CAIhB,CACD,eAAgB,CAEdqP,SAAU,yBAGRC,EAAkBtP,YAAO,MAAO,CACpCkH,KAAM,YACN/G,KAAM,YACNgH,kBAAmBA,CAACjD,EAAOkD,KACzB,MAAM,WACJC,GACEnD,EACJ,MAAO,CAACkD,EAAOmI,UAAWnI,EAAO,SAADE,OAAUC,YAAWF,EAAWmI,UAAW,GAPvDxP,EASrBsG,IAAA,IAAC,WACFe,GACDf,EAAA,OAAKH,YAAS,CACbsJ,OAAQ,OACR,eAAgB,CACdA,OAAQ,QAGVC,QAAS,GACc,UAAtBrI,EAAWmI,QAAsB,CAClC1G,QAAS,OACT6G,eAAgB,SAChBC,WAAY,UACW,SAAtBvI,EAAWmI,QAAqB,CACjCK,UAAW,OACXC,UAAW,SACX9E,UAAW,SACX,UAAW,CACT+E,QAAS,KACTjH,QAAS,eACTkH,cAAe,SACfP,OAAQ,OACR/G,MAAO,MAET,IACIuH,EAAcjQ,YAAOkQ,IAAO,CAChChJ,KAAM,YACN/G,KAAM,QACNgH,kBAAmBA,CAACjD,EAAOkD,KACzB,MAAM,WACJC,GACEnD,EACJ,MAAO,CAACkD,EAAO+I,MAAO/I,EAAO,cAADE,OAAeC,YAAWF,EAAWmI,UAAYpI,EAAO,aAADE,OAAcC,YAAWC,OAAOH,EAAWI,aAAeJ,EAAW+I,WAAahJ,EAAOiJ,eAAgBhJ,EAAWiJ,YAAclJ,EAAOmJ,gBAAgB,GAP5NvQ,EASjBoJ,IAAA,IAAC,MACFX,EAAK,WACLpB,GACD+B,EAAA,OAAKjD,YAAS,CACb2E,OAAQ,GACRuE,SAAU,WACVQ,UAAW,OAEX,eAAgB,CACdA,UAAW,UACXW,UAAW,SAEU,UAAtBnJ,EAAWmI,QAAsB,CAClC1G,QAAS,OACT2H,cAAe,SACfC,UAAW,qBACY,SAAtBrJ,EAAWmI,QAAqB,CACjC1G,QAAS,eACTkH,cAAe,SACfhF,UAAW,SACT3D,EAAWI,UAAY,CACzBA,SAAU,qBACe,OAAxBJ,EAAWI,UAAqB,CACjCA,SAAqC,OAA3BgB,EAAMS,YAAYS,KAAgBE,KAAKC,IAAIrB,EAAMS,YAAYG,OAAOU,GAAI,KAAO,GAAHzC,OAAMmB,EAAMS,YAAYG,OAAOU,IAAEzC,OAAGmB,EAAMS,YAAYS,MAC5I,CAAC,KAADrC,OAAMjH,IAAcsQ,kBAAoB,CACtC,CAAClI,EAAMS,YAAY0H,KAAK/G,KAAKC,IAAIrB,EAAMS,YAAYG,OAAOU,GAAI,KAAO,KAAU,CAC7EtC,SAAU,uBAGbJ,EAAWI,UAAoC,OAAxBJ,EAAWI,UAAqB,CACxDA,SAAU,GAAFH,OAAKmB,EAAMS,YAAYG,OAAOhC,EAAWI,WAASH,OAAGmB,EAAMS,YAAYS,MAC/E,CAAC,KAADrC,OAAMjH,IAAcsQ,kBAAoB,CACtC,CAAClI,EAAMS,YAAY0H,KAAKnI,EAAMS,YAAYG,OAAOhC,EAAWI,UAAY,KAAU,CAChFA,SAAU,uBAGbJ,EAAW+I,WAAa,CACzB1H,MAAO,qBACNrB,EAAWiJ,YAAc,CAC1BxF,OAAQ,EACRpC,MAAO,OACPjB,SAAU,OACVgI,OAAQ,OACRiB,UAAW,OACXnD,aAAc,EACd,CAAC,KAADjG,OAAMjH,IAAcsQ,kBAAoB,CACtC7F,OAAQ,EACRrD,SAAU,SAEZ,IAKIoJ,EAAsB3K,cAAiB,SAAgB2B,EAAShC,GACpE,MAAM3B,EAAQqE,YAAc,CAC1BrE,MAAO2D,EACPX,KAAM,cAEFuB,EAAQqI,cACRC,EAA4B,CAChCC,MAAOvI,EAAMoF,YAAYE,SAASkD,eAClCC,KAAMzI,EAAMoF,YAAYE,SAASoD,gBAG/B,mBAAoBC,EACpB,kBAAmBC,EAAkB,kBACrCC,EAAiB,cACjBC,EAAa,SACbpN,EAAQ,UACR6F,EAAS,qBACTwH,GAAuB,EAAK,WAC5BlB,GAAa,EAAK,UAClBF,GAAY,EAAK,SACjB3I,EAAW,KAAI,gBACfgK,EAAe,QACfC,EAAO,KACPC,EAAI,eACJC,EAAiB1B,IAAK,WACtB2B,EAAa,CAAC,EAAC,OACfrC,EAAS,QAAO,oBAChBsC,EAAsBC,IAAI,mBAC1BC,EAAqBjB,EAAyB,gBAC9CkB,GACE/N,EACJgG,EAAQlE,YAA8B9B,EAAO+B,GACzCoB,EAAalB,YAAS,CAAC,EAAGjC,EAAO,CACrCsN,uBACAlB,aACAF,YACA3I,WACA+H,WAEIvH,EAjKkBZ,KACxB,MAAM,QACJY,EAAO,OACPuH,EAAM,SACN/H,EAAQ,UACR2I,EAAS,WACTE,GACEjJ,EACEa,EAAQ,CACZ7G,KAAM,CAAC,QACPkO,UAAW,CAAC,YAAa,SAAFjI,OAAWC,YAAWiI,KAC7CW,MAAO,CAAC,QAAS,cAAF7I,OAAgBC,YAAWiI,IAAO,aAAAlI,OAAiBC,YAAWC,OAAOC,KAAc2I,GAAa,iBAAkBE,GAAc,oBAEjJ,OAAOnI,YAAeD,EAAOhI,IAAuB+H,EAAQ,EAoJ5CF,CAAkBV,GAC5B6K,EAAgBhM,WAmBhBiM,EAAiBC,YAAMf,GACvBgB,EAAqBnM,WAAc,KAChC,CACLoM,QAASH,KAEV,CAACA,IACJ,OAAoBhI,cAAKgF,EAAYhJ,YAAS,CAC5C6D,UAAWI,YAAKnC,EAAQ5G,KAAM2I,GAC9BuI,sBAAsB,EACtBC,WAAY,CACVzD,SAAUD,GAEZ2D,gBAAiB,CACfxD,SAAU9I,YAAS,CACjB6L,qBACAjM,GAAIuL,GACHC,IAELC,qBAAsBA,EACtBE,QAASA,EACTC,KAAMA,EACN9L,IAAKA,EACL6M,QAnC0BC,IAErBT,EAAczL,UAGnByL,EAAczL,QAAU,KACpBgL,GACFA,EAAgBkB,GAEdjB,GACFA,EAAQiB,EAAO,iBACjB,EAyBAtL,WAAYA,GACX6C,EAAO,CACR/F,SAAuBgG,cAAK2H,EAAqB3L,YAAS,CACxDyM,QAAQ,EACRC,GAAIlB,EACJmB,QAASd,EACTe,KAAM,gBACLd,EAAiB,CAClB9N,SAAuBgG,cAAKmF,EAAiB,CAC3CtF,UAAWI,YAAKnC,EAAQsH,WACxByD,YAnDkBL,IAGtBT,EAAczL,QAAUkM,EAAMxQ,SAAWwQ,EAAMM,aAAa,EAiDxD5L,WAAYA,EACZlD,SAAuBgG,cAAK8F,EAAa9J,YAAS,CAChDJ,GAAI6L,EACJsB,UAAW,GACXH,KAAM,SACN,mBAAoB3B,EACpB,kBAAmBe,GAClBN,EAAY,CACb7H,UAAWI,YAAKnC,EAAQkI,MAAO0B,EAAW7H,WAC1C3C,WAAYA,EACZlD,SAAuBgG,cAAKrK,IAAcqT,SAAU,CAClDzJ,MAAO2I,EACPlO,SAAUA,cAMtB,IAiIe0M,K,mCCrYf,wDAMIuC,EAAIC,YAAkBC,I,mCCN1B,sEAyCA,SAASC,EAAWC,GAChB,IAAIrP,EAAWqP,EAAGrP,SAAUsP,EAAWD,EAAGC,SAAUC,EAAKF,EAAGG,OAAQA,OAAgB,IAAPD,GAAwBA,EAC9CE,EAA9CC,YAAOC,oBAAUC,EAAaN,IAAY,GAAqB,GACpEO,EAAiBC,sBAAOpT,GAI5B,IAAKkT,EAAaN,GAAW,CACzB,IAAIS,EAAWT,EAASS,SAAUC,EAAiBC,YAAOX,EAAU,CAAC,aACrEO,EAAevN,QAAUyN,EACzBG,YAAaF,EACjB,CAWA,OAVAzN,qBAAU,WACFqN,EAAaN,IACbA,IAAWa,MAAK,SAAUd,GACtB,IAAIU,EAAWV,EAAGU,SAAUC,EAAiBC,YAAOZ,EAAI,CAAC,aACzDa,YAAaF,GACbH,EAAevN,QAAUyN,EACzBN,GAAY,EAChB,GAER,GAAG,IACK1N,gBAAoBqO,IAAYpB,SAAU,CAAEzJ,MAAO,CAAEwK,SAAUF,EAAevN,QAASkN,OAAQA,IAAYxP,EACvH,CACA,SAAS4P,EAAaN,GAClB,MAA2B,oBAAbA,CAClB,C,qHC9DIe,EAAa,EACjB,SAASC,IACL,IAAI9R,EAAK6R,EAET,OADAA,IACO7R,CACX,CACA,IAAI+R,EAAgB,SAAUlB,GAC1B,IAAIrP,EAAWqP,EAAGrP,SAAUwQ,EAAUnB,EAAGmB,QAASC,EAAYpB,EAAGoB,UAAWC,EAAiBrB,EAAGqB,eAAgBC,EAAStB,EAAGsB,OAAQC,EAAwBvB,EAAGuB,sBAC3JC,EAAmBC,YAAYC,GAC/BvS,EAAKsS,YAAYR,GACjBU,EAAUC,mBAAQ,WAAc,MAAQ,CACxCzS,GAAIA,EACJgS,QAASA,EACTC,UAAWA,EACXE,OAAQA,EACRD,eAAgB,SAAUQ,GACtBL,EAAiBzT,IAAI8T,GAAS,GAC9B,IAAIC,GAAc,EAClBN,EAAiB/R,SAAQ,SAAUsS,GAC1BA,IACDD,GAAc,EACtB,IACAA,IAAmC,OAAnBT,QAA8C,IAAnBA,GAAqCA,IACpF,EACAW,SAAU,SAAUH,GAEhB,OADAL,EAAiBzT,IAAI8T,GAAS,GACvB,WAAc,OAAOL,EAAiBS,OAAOJ,EAAU,CAClE,EACA,GAMJN,OAAwBlU,EAAY,CAAC+T,IAWrC,OAVAQ,mBAAQ,WACJJ,EAAiB/R,SAAQ,SAAUyS,EAAGvU,GAAO,OAAO6T,EAAiBzT,IAAIJ,GAAK,EAAQ,GAC1F,GAAG,CAACyT,IAKJ1O,aAAgB,YACX0O,IAAcI,EAAiBjR,OAA4B,OAAnB8Q,QAA8C,IAAnBA,GAAqCA,IAC7G,GAAG,CAACD,IACI1O,gBAAoByP,IAAgBxC,SAAU,CAAEzJ,MAAOyL,GAAWhR,EAC9E,EACA,SAAS+Q,IACL,OAAO,IAAI1U,GACf,C,YC/CA,SAASoV,EAAYC,GACjB,OAAOA,EAAM1U,KAAO,EACxB,CAqFA,IAAI2U,EAAkB,SAAUtC,GAC5B,IAAIrP,EAAWqP,EAAGrP,SAAU2Q,EAAStB,EAAGsB,OAAQpB,EAAKF,EAAGmB,QAASA,OAAiB,IAAPjB,GAAuBA,EAAImB,EAAiBrB,EAAGqB,eAAgBkB,EAAkBvC,EAAGuC,gBAAiBC,EAAKxC,EAAGuB,sBAAuBA,OAA+B,IAAPiB,GAAuBA,EAG1PC,EC9FR,WACI,IAAIC,EAAejC,kBAAO,GACtBT,EAAKK,YAAOC,mBAAS,GAAI,GAAIqC,EAAoB3C,EAAG,GAAI4C,EAAuB5C,EAAG,GAEtF,OADA6C,aAAiB,WAAc,OAAQH,EAAazP,SAAU,CAAO,IAC9D6P,uBAAY,YACdJ,EAAazP,SAAW2P,EAAqBD,EAAoB,EACtE,GAAG,CAACA,GACR,CDuFsBI,GACdC,EAAgBC,qBAAWC,KAC3BC,YAAeH,KACfP,EAAcO,EAAcI,aAEhC,IAAIC,EAAkB5C,kBAAO,GAEzB6C,EAlFR,SAAsB3S,GAClB,IAAI4S,EAAW,GAMf,OAJAC,WAAS/T,QAAQkB,GAAU,SAAU0R,GAC7BoB,yBAAepB,IACfkB,EAASnT,KAAKiS,EACtB,IACOkB,CACX,CA0E2BG,CAAa/S,GAGhCgT,EAAkBlD,iBAAO6C,GAEzBM,EAAcnD,iBAAO,IAAIzT,KACxBiG,QAED4Q,EAAUpD,iBAAO,IAAIqD,KAAO7Q,QAIhC,GA3GJ,SAA2BtC,EAAUiT,GAEjCjT,EAASlB,SAAQ,SAAU4S,GACvB,IAAI1U,EAAMyU,EAAYC,GAOtBuB,EAAY7V,IAAIJ,EAAK0U,EACzB,GACJ,CA4FI0B,CAAkBT,EAAkBM,GAGhCP,EAAgBpQ,QAEhB,OADAoQ,EAAgBpQ,SAAU,EAClBP,gBAAoBA,WAAgB,KAAM4Q,EAAiB1V,KAAI,SAAUyU,GAAS,OAAQ3P,gBAAoBwO,EAAe,CAAEvT,IAAKyU,EAAYC,GAAQjB,WAAW,EAAMD,UAASA,QAAU9T,EAAmBkU,sBAAuBA,GAAyBc,EAAS,KAUpR,IAPA,IAAI2B,EAAmBC,YAAc,GAAI5D,YAAOiD,IAG5CY,EAAcP,EAAgB1Q,QAAQrF,IAAIwU,GAC1C+B,EAAab,EAAiB1V,IAAIwU,GAElCgC,EAAaF,EAAYhY,OACpBJ,EAAI,EAAGA,EAAIsY,EAAYtY,IAAK,CACjC,IAAI6B,EAAMuW,EAAYpY,IACW,IAA7BqY,EAAWhY,QAAQwB,GACnBkW,EAAQQ,IAAI1W,GAIZkW,EAAQ5B,OAAOtU,EAEvB,CA2CA,OAxCI4U,GAAmBsB,EAAQtT,OAC3ByT,EAAmB,IAIvBH,EAAQpU,SAAQ,SAAU9B,GAEtB,IAAiC,IAA7BwW,EAAWhY,QAAQwB,GAAvB,CAEA,IAAI0U,EAAQuB,EAAY3V,IAAIN,GAC5B,GAAK0U,EAAL,CAEA,IAAIiC,EAAiBJ,EAAY/X,QAAQwB,GAczCqW,EAAiB3T,OAAOiU,EAAgB,EAAG5R,gBAAoBwO,EAAe,CAAEvT,IAAKyU,EAAYC,GAAQjB,WAAW,EAAOC,eAb9G,WACTuC,EAAY3B,OAAOtU,GACnBkW,EAAQ5B,OAAOtU,GAEf,IAAI4W,EAAcZ,EAAgB1Q,QAAQuR,WAAU,SAAUC,GAAgB,OAAOA,EAAa9W,MAAQA,CAAK,IAC/GgW,EAAgB1Q,QAAQ5C,OAAOkU,EAAa,GAEvCV,EAAQtT,OACToT,EAAgB1Q,QAAUqQ,EAC1Bb,IACApB,GAAkBA,IAE1B,EACmJC,OAAQA,EAAQC,sBAAuBA,GAAyBc,GAfzM,CAHA,CAmBd,IAGA2B,EAAmBA,EAAiBpW,KAAI,SAAUyU,GAC9C,IAAI1U,EAAM0U,EAAM1U,IAChB,OAAOkW,EAAQ/V,IAAIH,GAAQ0U,EAAU3P,gBAAoBwO,EAAe,CAAEvT,IAAKyU,EAAYC,GAAQjB,WAAW,EAAMG,sBAAuBA,GAAyBc,EACxK,IACAsB,EAAgB1Q,QAAU+Q,EAMlBtR,gBAAoBA,WAAgB,KAAMmR,EAAQtT,KACpDyT,EACAA,EAAiBpW,KAAI,SAAUyU,GAAS,OAAOqC,uBAAarC,EAAQ,IAC9E,C,0GEhLA,SAASsC,IAIL,IAAIC,GAAa,EAKbC,EAAoB,GAIpBC,EAAc,IAAIhB,IAClBiB,EAAW,CACXC,UAAW,SAAUC,GAEjB,OADAH,EAAYT,IAAIY,GACT,WAA0BH,EAAY7C,OAAOgD,EAAgB,CACxE,EACAC,MAAO,SAAUC,EAAYC,GAOzB,GAAIR,EAAY,CACZ,IAAIS,EAAe,GAMnB,OALAP,EAAYrV,SAAQ,SAAUwV,GAC1BI,EAAajV,KAAKkV,YAAqBL,EAAeE,EAAY,CAC9DC,mBAAoBA,IAE5B,IACOG,QAAQC,IAAIH,EACvB,CAEI,OAAO,IAAIE,SAAQ,SAAUE,GACzBZ,EAAkBzU,KAAK,CACnBsV,UAAW,CAACP,EAAYC,GACxBK,QAASA,GAEjB,GAER,EACA1X,IAAK,SAAUoX,GAEX,OADAQ,YAAUf,EAAY,iHACfE,EAAYrV,SAAQ,SAAUwV,GACjCW,YAAUX,EAAeE,EAC7B,GACJ,EACAU,KAAM,WACFf,EAAYrV,SAAQ,SAAUwV,GAC1Ba,YAAcb,EAClB,GACJ,EACAc,MAAO,WAMH,OALAnB,GAAa,EACbC,EAAkBpV,SAAQ,SAAUuQ,GAChC,IAAI0F,EAAY1F,EAAG0F,UAAWD,EAAUzF,EAAGyF,QAC3CV,EAASG,MAAMc,MAAMjB,EAAUd,YAAc,GAAI5D,YAAOqF,KAAa5E,KAAK2E,EAC9E,IACO,WACHb,GAAa,EACbG,EAASc,MACb,CACJ,GAEJ,OAAOd,CACX,C,oBCvBA,SAASkB,IACL,IAAIlB,EAAWtD,YAAYkD,GAE3B,OADAzR,oBAAU6R,EAASgB,MAAO,IACnBhB,CACX,C,0HCvDO,SAASmB,EAAmBvZ,GACjC,OAAOC,YAAqB,SAAUD,EACxC,CAEewZ,MADIrZ,YAAuB,SAAU,CAAC,OAAQ,UAAW,YAAa,WAAY,WAAY,eAAgB,WAAY,eAAgB,YAAa,aAAc,YAAa,OAAQ,QAAS,UAAW,Y,eCH7N,MAAM2F,EAAY,CAAC,WAAY,YAAa,QAAS,YAAa,WAAY,qBAAsB,wBAAyB,OAAQ,WAwB/H2T,EAAU5Z,YAAOiN,IAAY,CACjC/F,KAAM,SACN/G,KAAM,OACN0Z,kBAAmBC,GAAQC,YAAsBD,IAAkB,YAATA,EAC1D3S,kBAAmBA,CAACjD,EAAOkD,KACzB,MAAM,WACJC,GACEnD,EACJ,MAAO,CAACkD,EAAO/F,KAAM+F,EAAOC,EAAWoD,SAAUrD,EAAO,OAADE,OAAQC,YAAWF,EAAWtD,QAA+B,YAArBsD,EAAWoF,OAAuBrF,EAAO4S,aAAc5S,EAAOG,YAAWF,EAAWtD,OAAQqD,EAAOC,EAAWoF,OAAO,GARxMzM,EAUbsG,IAGG,IAHF,MACFmC,EAAK,WACLpB,GACDf,EACC,IAAI2T,EAAuBC,EAC3B,OAAO/T,YAAS,CAAC,EAAGsC,EAAMsC,WAAWoP,OAAQ,CAC3CC,UAAW,GACXxM,WAAYnF,EAAMoF,YAAYC,OAAO,CAAC,mBAAoB,aAAc,gBAAiB,CACvFC,SAAUtF,EAAMoF,YAAYE,SAASsM,QAEvC9M,aAAc,MACdD,QAAS,EACTgN,SAAU,EACV5R,MAAO,GACP+G,OAAQ,GACRP,QAASzG,EAAM+E,MAAQ/E,GAAOyG,OAAOqL,IACrC/J,WAAY/H,EAAM+E,MAAQ/E,GAAO+R,QAAQ,GACzC,WAAY,CACVhK,WAAY/H,EAAM+E,MAAQ/E,GAAO+R,QAAQ,KAE3C/N,MAAOhE,EAAM+E,KAAO/E,EAAM+E,KAAKC,QAAQgN,KAAKvO,QAAwF,OAA7E+N,GAAyBC,EAAiBzR,EAAMgF,SAASiN,sBAA2B,EAAST,EAAsBpa,KAAKqa,EAAgBzR,EAAMgF,QAAQkN,KAAK,MAClNzM,iBAAkBzF,EAAM+E,MAAQ/E,GAAOgF,QAAQkN,KAAK,KACpD,UAAW,CACTzM,iBAAkBzF,EAAM+E,MAAQ/E,GAAOgF,QAAQkN,KAAKC,KAEpD,uBAAwB,CACtB1M,iBAAkBzF,EAAM+E,MAAQ/E,GAAOgF,QAAQkN,KAAK,MAEtDE,eAAgB,QAElB,CAAC,KAADvT,OAAMqS,EAAWmB,eAAiB,CAChCtK,WAAY/H,EAAM+E,MAAQ/E,GAAO+R,QAAQ,KAEtB,UAApBnT,EAAWtD,MAAoB,CAChC2E,MAAO,GACP+G,OAAQ,IACa,WAApBpI,EAAWtD,MAAqB,CACjC2E,MAAO,GACP+G,OAAQ,IACgB,aAAvBpI,EAAWoD,SAA0B,CACtC8C,aAAc,GACdD,QAAS,SACT5E,MAAO,OACP0R,UAAW,OACXE,SAAU,GACV7K,OAAQ,IACgB,aAAvBpI,EAAWoD,SAA8C,UAApBpD,EAAWtD,MAAoB,CACrE2E,MAAO,OACP4E,QAAS,QACTC,aAAc,GACd+M,SAAU,GACV7K,OAAQ,IACgB,aAAvBpI,EAAWoD,SAA8C,WAApBpD,EAAWtD,MAAqB,CACtE2E,MAAO,OACP4E,QAAS,SACTC,aAAc,GACd+M,SAAU,GACV7K,OAAQ,IACc,YAArBpI,EAAWoF,OAAuB,CACnCA,MAAO,WACP,IACDrD,IAAA,IAAC,MACFX,EAAK,WACLpB,GACD+B,EAAA,OAAKjD,YAAS,CAAC,EAAwB,YAArBkB,EAAWoF,OAA4C,YAArBpF,EAAWoF,OAA0E,OAAlDhE,EAAM+E,MAAQ/E,GAAOgF,QAAQpG,EAAWoF,QAAkB,CAChJA,OAAQhE,EAAM+E,MAAQ/E,GAAOgF,QAAQpG,EAAWoF,OAAOsO,aACvD7M,iBAAkBzF,EAAM+E,MAAQ/E,GAAOgF,QAAQpG,EAAWoF,OAAO8B,KACjE,UAAW,CACTL,iBAAkBzF,EAAM+E,MAAQ/E,GAAOgF,QAAQpG,EAAWoF,OAAOuO,KAEjE,uBAAwB,CACtB9M,iBAAkBzF,EAAM+E,MAAQ/E,GAAOgF,QAAQpG,EAAWoF,OAAO8B,QAGrE,IAAE3E,IAAA,IAAC,MACHnB,GACDmB,EAAA,MAAM,CACL,CAAC,KAADtC,OAAMqS,EAAWlL,WAAa,CAC5BhC,OAAQhE,EAAM+E,MAAQ/E,GAAOgF,QAAQC,OAAOe,SAC5C+B,WAAY/H,EAAM+E,MAAQ/E,GAAO+R,QAAQ,GACzCtM,iBAAkBzF,EAAM+E,MAAQ/E,GAAOgF,QAAQC,OAAOuN,oBAEzD,IACKC,EAAmBhV,cAAiB,SAAa2B,EAAShC,GAC9D,MAAM3B,EAAQqE,YAAc,CAC1BrE,MAAO2D,EACPX,KAAM,YAEF,SACF/C,EAAQ,UACR6F,EAAS,MACTyC,EAAQ,UAAS,UACjBxC,EAAY,SAAQ,SACpBwE,GAAW,EAAK,mBAChBE,GAAqB,EAAK,sBAC1BwM,EAAqB,KACrBpX,EAAO,QAAO,QACd0G,EAAU,YACRvG,EACJgG,EAAQlE,YAA8B9B,EAAO+B,GACzCoB,EAAalB,YAAS,CAAC,EAAGjC,EAAO,CACrCuI,QACAxC,YACAwE,WACAE,qBACA5K,OACA0G,YAEIxC,EAnIkBZ,KACxB,MAAM,MACJoF,EAAK,QACLhC,EAAO,QACPxC,EAAO,KACPlE,GACEsD,EACEa,EAAQ,CACZ7G,KAAM,CAAC,OAAQoJ,EAAS,OAAFnD,OAASC,YAAWxD,IAAmB,YAAV0I,EAAsB,eAAiBA,IAEtF2O,EAAkBjT,YAAeD,EAAOwR,EAAoBzR,GAClE,OAAO9B,YAAS,CAAC,EAAG8B,EAASmT,EAAgB,EAwH7BrT,CAAkBV,GAClC,OAAoB8C,cAAKyP,EAASzT,YAAS,CACzC6D,UAAWI,YAAKnC,EAAQ5G,KAAM2I,GAC9BC,UAAWA,EACXwE,SAAUA,EACVI,aAAcF,EACdwM,sBAAuB/Q,YAAKnC,EAAQ6S,aAAcK,GAClD9T,WAAYA,EACZxB,IAAKA,GACJqE,EAAO,CACRjC,QAASA,EACT9D,SAAUA,IAEd,IAqEe+W,K", "file": "static/js/4.59093a73.chunk.js", "sourcesContent": ["import objectWithoutPropertiesLoose from \"./objectWithoutPropertiesLoose.js\";\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nexport { _objectWithoutProperties as default };", "import { createContext } from 'react';\nconst DialogContext = /*#__PURE__*/createContext({});\nif (process.env.NODE_ENV !== 'production') {\n  DialogContext.displayName = 'DialogContext';\n}\nexport default DialogContext;", "import createStyled from './createStyled';\nconst styled = createStyled();\nexport default styled;", "import { unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nimport generateUtilityClass from '../generateUtilityClass';\nexport function getDialogUtilityClass(slot) {\n  return generateUtilityClass('MuiDialog', slot);\n}\nconst dialogClasses = generateUtilityClasses('MuiDialog', ['root', 'scrollPaper', 'scrollBody', 'container', 'paper', 'paperScrollPaper', 'paperScrollBody', 'paperWidthFalse', 'paperWidthXs', 'paperWidthSm', 'paperWidthMd', 'paperWidthLg', 'paperWidthXl', 'paperFullWidth', 'paperFullScreen']);\nexport default dialogClasses;", "import { ObserverInstanceCallback } from './index';\n\nconst observerMap = new Map<\n  string,\n  {\n    id: string;\n    observer: IntersectionObserver;\n    elements: Map<Element, Array<ObserverInstanceCallback>>;\n  }\n>();\n\nconst RootIds: WeakMap<Element | Document, string> = new WeakMap();\nlet rootId = 0;\n\nlet unsupportedValue: boolean | undefined = undefined;\n\n/**\n * What should be the default behavior if the IntersectionObserver is unsupported?\n * Ideally the polyfill has been loaded, you can have the following happen:\n * - `undefined`: Throw an error\n * - `true` or `false`: Set the `inView` value to this regardless of intersection state\n * **/\nexport function defaultFallbackInView(inView: boolean | undefined) {\n  unsupportedValue = inView;\n}\n\n/**\n * Generate a unique ID for the root element\n * @param root\n */\nfunction getRootId(root: IntersectionObserverInit['root']) {\n  if (!root) return '0';\n  if (RootIds.has(root)) return RootIds.get(root);\n  rootId += 1;\n  RootIds.set(root, rootId.toString());\n  return RootIds.get(root);\n}\n\n/**\n * Convert the options to a string Id, based on the values.\n * Ensures we can reuse the same observer when observing elements with the same options.\n * @param options\n */\nexport function optionsToId(options: IntersectionObserverInit) {\n  return Object.keys(options)\n    .sort()\n    .filter((key) => options[key] !== undefined)\n    .map((key) => {\n      return `${key}_${\n        key === 'root' ? getRootId(options.root) : options[key]\n      }`;\n    })\n    .toString();\n}\n\nfunction createObserver(options: IntersectionObserverInit) {\n  // Create a unique ID for this observer instance, based on the root, root margin and threshold.\n  let id = optionsToId(options);\n  let instance = observerMap.get(id);\n\n  if (!instance) {\n    // Create a map of elements this observer is going to observe. Each element has a list of callbacks that should be triggered, once it comes into view.\n    const elements = new Map<Element, Array<ObserverInstanceCallback>>();\n    let thresholds: number[] | readonly number[];\n\n    const observer = new IntersectionObserver((entries) => {\n      entries.forEach((entry) => {\n        // While it would be nice if you could just look at isIntersecting to determine if the component is inside the viewport, browsers can't agree on how to use it.\n        // -Firefox ignores `threshold` when considering `isIntersecting`, so it will never be false again if `threshold` is > 0\n        const inView =\n          entry.isIntersecting &&\n          thresholds.some((threshold) => entry.intersectionRatio >= threshold);\n\n        // @ts-ignore support IntersectionObserver v2\n        if (options.trackVisibility && typeof entry.isVisible === 'undefined') {\n          // The browser doesn't support Intersection Observer v2, falling back to v1 behavior.\n          // @ts-ignore\n          entry.isVisible = inView;\n        }\n\n        elements.get(entry.target)?.forEach((callback) => {\n          callback(inView, entry);\n        });\n      });\n    }, options);\n\n    // Ensure we have a valid thresholds array. If not, use the threshold from the options\n    thresholds =\n      observer.thresholds ||\n      (Array.isArray(options.threshold)\n        ? options.threshold\n        : [options.threshold || 0]);\n\n    instance = {\n      id,\n      observer,\n      elements,\n    };\n\n    observerMap.set(id, instance);\n  }\n\n  return instance;\n}\n\n/**\n * @param element - DOM Element to observe\n * @param callback - Callback function to trigger when intersection status changes\n * @param options - Intersection Observer options\n * @param fallbackInView - Fallback inView value.\n * @return Function - Cleanup function that should be triggered to unregister the observer\n */\nexport function observe(\n  element: Element,\n  callback: ObserverInstanceCallback,\n  options: IntersectionObserverInit = {},\n  fallbackInView = unsupportedValue,\n) {\n  if (\n    typeof window.IntersectionObserver === 'undefined' &&\n    fallbackInView !== undefined\n  ) {\n    const bounds = element.getBoundingClientRect();\n    callback(fallbackInView, {\n      isIntersecting: fallbackInView,\n      target: element,\n      intersectionRatio:\n        typeof options.threshold === 'number' ? options.threshold : 0,\n      time: 0,\n      boundingClientRect: bounds,\n      intersectionRect: bounds,\n      rootBounds: bounds,\n    });\n    return () => {\n      // Nothing to cleanup\n    };\n  }\n  // An observer with the same options can be reused, so lets use this fact\n  const { id, observer, elements } = createObserver(options);\n\n  // Register the callback listener for this element\n  let callbacks = elements.get(element) || [];\n  if (!elements.has(element)) {\n    elements.set(element, callbacks);\n  }\n\n  callbacks.push(callback);\n  observer.observe(element);\n\n  return function unobserve() {\n    // Remove the callback from the callback list\n    callbacks.splice(callbacks.indexOf(callback), 1);\n\n    if (callbacks.length === 0) {\n      // No more callback exists for element, so destroy it\n      elements.delete(element);\n      observer.unobserve(element);\n    }\n\n    if (elements.size === 0) {\n      // No more elements are being observer by this instance, so destroy it\n      observer.disconnect();\n      observerMap.delete(id);\n    }\n  };\n}\n", "import * as React from 'react';\nimport { IntersectionObserverProps, PlainChildrenProps } from './index';\nimport { observe } from './observe';\n\ntype State = {\n  inView: boolean;\n  entry?: IntersectionObserverEntry;\n};\n\nfunction isPlainChildren(\n  props: IntersectionObserverProps | PlainChildrenProps,\n): props is PlainChildrenProps {\n  return typeof props.children !== 'function';\n}\n\n/**\n ## Render props\n\n To use the `<InView>` component, you pass it a function. It will be called\n whenever the state changes, with the new value of `inView`. In addition to the\n `inView` prop, children also receive a `ref` that should be set on the\n containing DOM element. This is the element that the IntersectionObserver will\n monitor.\n\n If you need it, you can also access the\n [`IntersectionObserverEntry`](https://developer.mozilla.org/en-US/docs/Web/API/IntersectionObserverEntry)\n on `entry`, giving you access to all the details about the current intersection\n state.\n\n ```jsx\n import { InView } from 'react-intersection-observer';\n\n const Component = () => (\n <InView>\n {({ inView, ref, entry }) => (\n      <div ref={ref}>\n        <h2>{`Header inside viewport ${inView}.`}</h2>\n      </div>\n    )}\n </InView>\n );\n\n export default Component;\n ```\n\n ## Plain children\n\n You can pass any element to the `<InView />`, and it will handle creating the\n wrapping DOM element. Add a handler to the `onChange` method, and control the\n state in your own component. Any extra props you add to `<InView>` will be\n passed to the HTML element, allowing you set the `className`, `style`, etc.\n\n ```jsx\n import { InView } from 'react-intersection-observer';\n\n const Component = () => (\n <InView as=\"div\" onChange={(inView, entry) => console.log('Inview:', inView)}>\n <h2>Plain children are always rendered. Use onChange to monitor state.</h2>\n </InView>\n );\n\n export default Component;\n ```\n */\nexport class InView extends React.Component<\n  IntersectionObserverProps | PlainChildrenProps,\n  State\n> {\n  static displayName = 'InView';\n  static defaultProps = {\n    threshold: 0,\n    triggerOnce: false,\n    initialInView: false,\n  };\n\n  constructor(props: IntersectionObserverProps | PlainChildrenProps) {\n    super(props);\n    this.state = {\n      inView: !!props.initialInView,\n      entry: undefined,\n    };\n  }\n\n  componentDidUpdate(prevProps: IntersectionObserverProps) {\n    // If a IntersectionObserver option changed, reinit the observer\n    if (\n      prevProps.rootMargin !== this.props.rootMargin ||\n      prevProps.root !== this.props.root ||\n      prevProps.threshold !== this.props.threshold ||\n      prevProps.skip !== this.props.skip ||\n      prevProps.trackVisibility !== this.props.trackVisibility ||\n      prevProps.delay !== this.props.delay\n    ) {\n      this.unobserve();\n      this.observeNode();\n    }\n  }\n\n  componentWillUnmount() {\n    this.unobserve();\n    this.node = null;\n  }\n\n  node: Element | null = null;\n  _unobserveCb: (() => void) | null = null;\n\n  observeNode() {\n    if (!this.node || this.props.skip) return;\n    const {\n      threshold,\n      root,\n      rootMargin,\n      trackVisibility,\n      delay,\n      fallbackInView,\n    } = this.props;\n\n    this._unobserveCb = observe(\n      this.node,\n      this.handleChange,\n      {\n        threshold,\n        root,\n        rootMargin,\n        // @ts-ignore\n        trackVisibility,\n        // @ts-ignore\n        delay,\n      },\n      fallbackInView,\n    );\n  }\n\n  unobserve() {\n    if (this._unobserveCb) {\n      this._unobserveCb();\n      this._unobserveCb = null;\n    }\n  }\n\n  handleNode = (node?: Element | null) => {\n    if (this.node) {\n      // Clear the old observer, before we start observing a new element\n      this.unobserve();\n\n      if (!node && !this.props.triggerOnce && !this.props.skip) {\n        // Reset the state if we get a new node, and we aren't ignoring updates\n        this.setState({ inView: !!this.props.initialInView, entry: undefined });\n      }\n    }\n\n    this.node = node ? node : null;\n    this.observeNode();\n  };\n\n  handleChange = (inView: boolean, entry: IntersectionObserverEntry) => {\n    if (inView && this.props.triggerOnce) {\n      // If `triggerOnce` is true, we should stop observing the element.\n      this.unobserve();\n    }\n    if (!isPlainChildren(this.props)) {\n      // Store the current State, so we can pass it to the children in the next render update\n      // There's no reason to update the state for plain children, since it's not used in the rendering.\n      this.setState({ inView, entry });\n    }\n    if (this.props.onChange) {\n      // If the user is actively listening for onChange, always trigger it\n      this.props.onChange(inView, entry);\n    }\n  };\n\n  render() {\n    if (!isPlainChildren(this.props)) {\n      const { inView, entry } = this.state;\n      return this.props.children({ inView, entry, ref: this.handleNode });\n    }\n\n    const {\n      children,\n      as,\n      triggerOnce,\n      threshold,\n      root,\n      rootMargin,\n      onChange,\n      skip,\n      trackVisibility,\n      delay,\n      initialInView,\n      fallbackInView,\n      ...props\n    } = this.props;\n\n    return React.createElement(\n      as || 'div',\n      { ref: this.handleNode, ...props },\n      children,\n    );\n  }\n}\n", "import * as React from 'react';\nimport { InViewHookResponse, IntersectionOptions } from './index';\nimport { useEffect } from 'react';\nimport { observe } from './observe';\n\ntype State = {\n  inView: boolean;\n  entry?: IntersectionObserverEntry;\n};\n\n/**\n * React Hooks make it easy to monitor the `inView` state of your components. Call\n * the `useInView` hook with the (optional) [options](#options) you need. It will\n * return an array containing a `ref`, the `inView` status and the current\n * [`entry`](https://developer.mozilla.org/en-US/docs/Web/API/IntersectionObserverEntry).\n * Assign the `ref` to the DOM element you want to monitor, and the hook will\n * report the status.\n *\n * @example\n * ```jsx\n * import React from 'react';\n * import { useInView } from 'react-intersection-observer';\n *\n * const Component = () => {\n *   const { ref, inView, entry } = useInView({\n *       threshold: 0,\n *   });\n *\n *   return (\n *     <div ref={ref}>\n *       <h2>{`Header inside viewport ${inView}.`}</h2>\n *     </div>\n *   );\n * };\n * ```\n */\nexport function useInView({\n  threshold,\n  delay,\n  trackVisibility,\n  rootMargin,\n  root,\n  triggerOnce,\n  skip,\n  initialInView,\n  fallbackInView,\n}: IntersectionOptions = {}): InViewHookResponse {\n  const unobserve = React.useRef<Function>();\n  const [state, setState] = React.useState<State>({\n    inView: !!initialInView,\n  });\n  const setRef = React.useCallback(\n    (node: Element | null) => {\n      if (unobserve.current !== undefined) {\n        unobserve.current();\n        unobserve.current = undefined;\n      }\n\n      // Skip creating the observer\n      if (skip) return;\n\n      if (node) {\n        unobserve.current = observe(\n          node,\n          (inView, entry) => {\n            setState({ inView, entry });\n\n            if (entry.isIntersecting && triggerOnce && unobserve.current) {\n              // If it should only trigger once, unobserve the element after it's inView\n              unobserve.current();\n              unobserve.current = undefined;\n            }\n          },\n          {\n            root,\n            rootMargin,\n            threshold,\n            // @ts-ignore\n            trackVisibility,\n            // @ts-ignore\n            delay,\n          },\n          fallbackInView,\n        );\n      }\n    },\n    // We break the rule here, because we aren't including the actual `threshold` variable\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n      // If the threshold is an array, convert it to a string so it won't change between renders.\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      Array.isArray(threshold) ? threshold.toString() : threshold,\n      root,\n      rootMargin,\n      triggerOnce,\n      skip,\n      trackVisibility,\n      fallbackInView,\n      delay,\n    ],\n  );\n\n  /* eslint-disable-next-line */\n  useEffect(() => {\n    if (!unobserve.current && state.entry && !triggerOnce && !skip) {\n      // If we don't have a ref, then reset the state (unless the hook is set to only `triggerOnce` or `skip`)\n      // This ensures we correctly reflect the current state - If you aren't observing anything, then nothing is inView\n      setState({\n        inView: !!initialInView,\n      });\n    }\n  });\n\n  const result = [setRef, state.inView, state.entry] as InViewHookResponse;\n\n  // Support object destructuring, by adding the specific values.\n  result.ref = result[0];\n  result.inView = result[1];\n  result.entry = result[2];\n\n  return result;\n}\n", "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"component\", \"disableGutters\", \"fixed\", \"maxWidth\", \"classes\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport composeClasses from '@mui/utils/composeClasses';\nimport capitalize from '@mui/utils/capitalize';\nimport useThemePropsSystem from '../useThemeProps';\nimport systemStyled from '../styled';\nimport createTheme from '../createTheme';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultTheme = createTheme();\nconst defaultCreateStyledComponent = systemStyled('div', {\n  name: '<PERSON><PERSON><PERSON>ontainer',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[`maxWidth${capitalize(String(ownerState.maxWidth))}`], ownerState.fixed && styles.fixed, ownerState.disableGutters && styles.disableGutters];\n  }\n});\nconst useThemePropsDefault = inProps => useThemePropsSystem({\n  props: inProps,\n  name: 'MuiContainer',\n  defaultTheme\n});\nconst useUtilityClasses = (ownerState, componentName) => {\n  const getContainerUtilityClass = slot => {\n    return generateUtilityClass(componentName, slot);\n  };\n  const {\n    classes,\n    fixed,\n    disableGutters,\n    maxWidth\n  } = ownerState;\n  const slots = {\n    root: ['root', maxWidth && `maxWidth${capitalize(String(maxWidth))}`, fixed && 'fixed', disableGutters && 'disableGutters']\n  };\n  return composeClasses(slots, getContainerUtilityClass, classes);\n};\nexport default function createContainer(options = {}) {\n  const {\n    // This will allow adding custom styled fn (for example for custom sx style function)\n    createStyledComponent = defaultCreateStyledComponent,\n    useThemeProps = useThemePropsDefault,\n    componentName = 'MuiContainer'\n  } = options;\n  const ContainerRoot = createStyledComponent(({\n    theme,\n    ownerState\n  }) => _extends({\n    width: '100%',\n    marginLeft: 'auto',\n    boxSizing: 'border-box',\n    marginRight: 'auto',\n    display: 'block'\n  }, !ownerState.disableGutters && {\n    paddingLeft: theme.spacing(2),\n    paddingRight: theme.spacing(2),\n    // @ts-ignore module augmentation fails if custom breakpoints are used\n    [theme.breakpoints.up('sm')]: {\n      paddingLeft: theme.spacing(3),\n      paddingRight: theme.spacing(3)\n    }\n  }), ({\n    theme,\n    ownerState\n  }) => ownerState.fixed && Object.keys(theme.breakpoints.values).reduce((acc, breakpointValueKey) => {\n    const breakpoint = breakpointValueKey;\n    const value = theme.breakpoints.values[breakpoint];\n    if (value !== 0) {\n      // @ts-ignore\n      acc[theme.breakpoints.up(breakpoint)] = {\n        maxWidth: `${value}${theme.breakpoints.unit}`\n      };\n    }\n    return acc;\n  }, {}), ({\n    theme,\n    ownerState\n  }) => _extends({}, ownerState.maxWidth === 'xs' && {\n    // @ts-ignore module augmentation fails if custom breakpoints are used\n    [theme.breakpoints.up('xs')]: {\n      // @ts-ignore module augmentation fails if custom breakpoints are used\n      maxWidth: Math.max(theme.breakpoints.values.xs, 444)\n    }\n  }, ownerState.maxWidth &&\n  // @ts-ignore module augmentation fails if custom breakpoints are used\n  ownerState.maxWidth !== 'xs' && {\n    // @ts-ignore module augmentation fails if custom breakpoints are used\n    [theme.breakpoints.up(ownerState.maxWidth)]: {\n      // @ts-ignore module augmentation fails if custom breakpoints are used\n      maxWidth: `${theme.breakpoints.values[ownerState.maxWidth]}${theme.breakpoints.unit}`\n    }\n  }));\n  const Container = /*#__PURE__*/React.forwardRef(function Container(inProps, ref) {\n    const props = useThemeProps(inProps);\n    const {\n        className,\n        component = 'div',\n        disableGutters = false,\n        fixed = false,\n        maxWidth = 'lg'\n      } = props,\n      other = _objectWithoutPropertiesLoose(props, _excluded);\n    const ownerState = _extends({}, props, {\n      component,\n      disableGutters,\n      fixed,\n      maxWidth\n    });\n\n    // @ts-ignore module augmentation fails if custom breakpoints are used\n    const classes = useUtilityClasses(ownerState, componentName);\n    return (\n      /*#__PURE__*/\n      // @ts-ignore theme is injected by the styled util\n      _jsx(ContainerRoot, _extends({\n        as: component\n        // @ts-ignore module augmentation fails if custom breakpoints are used\n        ,\n        ownerState: ownerState,\n        className: clsx(classes.root, className),\n        ref: ref\n      }, other))\n    );\n  });\n  process.env.NODE_ENV !== \"production\" ? Container.propTypes /* remove-proptypes */ = {\n    children: PropTypes.node,\n    classes: PropTypes.object,\n    className: PropTypes.string,\n    component: PropTypes.elementType,\n    disableGutters: PropTypes.bool,\n    fixed: PropTypes.bool,\n    maxWidth: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl', false]), PropTypes.string]),\n    sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n  } : void 0;\n  return Container;\n}", "/* eslint-disable material-ui/mui-name-matches-component-name */\nimport PropTypes from 'prop-types';\nimport { createContainer } from '@mui/system';\nimport capitalize from '../utils/capitalize';\nimport styled from '../styles/styled';\nimport useThemeProps from '../styles/useThemeProps';\nconst Container = createContainer({\n  createStyledComponent: styled('div', {\n    name: '<PERSON><PERSON><PERSON>ontaine<PERSON>',\n    slot: 'Root',\n    overridesResolver: (props, styles) => {\n      const {\n        ownerState\n      } = props;\n      return [styles.root, styles[`maxWidth${capitalize(String(ownerState.maxWidth))}`], ownerState.fixed && styles.fixed, ownerState.disableGutters && styles.disableGutters];\n    }\n  }),\n  useThemeProps: inProps => useThemeProps({\n    props: inProps,\n    name: 'MuiContainer'\n  })\n});\nprocess.env.NODE_ENV !== \"production\" ? Container.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the left and right padding is removed.\n   * @default false\n   */\n  disableGutters: PropTypes.bool,\n  /**\n   * Set the max-width to match the min-width of the current breakpoint.\n   * This is useful if you'd prefer to design for a fixed set of sizes\n   * instead of trying to accommodate a fully fluid viewport.\n   * It's fluid by default.\n   * @default false\n   */\n  fixed: PropTypes.bool,\n  /**\n   * Determine the max-width of the container.\n   * The container width grows with the size of the screen.\n   * Set to `false` to disable `maxWidth`.\n   * @default 'lg'\n   */\n  maxWidth: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl', false]), PropTypes.string]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Container;", "import { unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nimport generateUtilityClass from '../generateUtilityClass';\nexport function getTypographyUtilityClass(slot) {\n  return generateUtilityClass('MuiTypography', slot);\n}\nconst typographyClasses = generateUtilityClasses('MuiTypography', ['root', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'subtitle1', 'subtitle2', 'body1', 'body2', 'inherit', 'button', 'caption', 'overline', 'alignLeft', 'alignRight', 'alignCenter', 'alignJustify', 'noWrap', 'gutterBottom', 'paragraph']);\nexport default typographyClasses;", "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"align\", \"className\", \"component\", \"gutterBottom\", \"noWrap\", \"paragraph\", \"variant\", \"variantMapping\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { unstable_extendSxProp as extendSxProp } from '@mui/system';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport styled from '../styles/styled';\nimport useThemeProps from '../styles/useThemeProps';\nimport capitalize from '../utils/capitalize';\nimport { getTypographyUtilityClass } from './typographyClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    align,\n    gutterBottom,\n    noWrap,\n    paragraph,\n    variant,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', variant, ownerState.align !== 'inherit' && `align${capitalize(align)}`, gutterBottom && 'gutterBottom', noWrap && 'noWrap', paragraph && 'paragraph']\n  };\n  return composeClasses(slots, getTypographyUtilityClass, classes);\n};\nexport const TypographyRoot = styled('span', {\n  name: 'MuiTypography',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.variant && styles[ownerState.variant], ownerState.align !== 'inherit' && styles[`align${capitalize(ownerState.align)}`], ownerState.noWrap && styles.noWrap, ownerState.gutterBottom && styles.gutterBottom, ownerState.paragraph && styles.paragraph];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  margin: 0\n}, ownerState.variant && theme.typography[ownerState.variant], ownerState.align !== 'inherit' && {\n  textAlign: ownerState.align\n}, ownerState.noWrap && {\n  overflow: 'hidden',\n  textOverflow: 'ellipsis',\n  whiteSpace: 'nowrap'\n}, ownerState.gutterBottom && {\n  marginBottom: '0.35em'\n}, ownerState.paragraph && {\n  marginBottom: 16\n}));\nconst defaultVariantMapping = {\n  h1: 'h1',\n  h2: 'h2',\n  h3: 'h3',\n  h4: 'h4',\n  h5: 'h5',\n  h6: 'h6',\n  subtitle1: 'h6',\n  subtitle2: 'h6',\n  body1: 'p',\n  body2: 'p',\n  inherit: 'p'\n};\n\n// TODO v6: deprecate these color values in v5.x and remove the transformation in v6\nconst colorTransformations = {\n  primary: 'primary.main',\n  textPrimary: 'text.primary',\n  secondary: 'secondary.main',\n  textSecondary: 'text.secondary',\n  error: 'error.main'\n};\nconst transformDeprecatedColors = color => {\n  return colorTransformations[color] || color;\n};\nconst Typography = /*#__PURE__*/React.forwardRef(function Typography(inProps, ref) {\n  const themeProps = useThemeProps({\n    props: inProps,\n    name: 'MuiTypography'\n  });\n  const color = transformDeprecatedColors(themeProps.color);\n  const props = extendSxProp(_extends({}, themeProps, {\n    color\n  }));\n  const {\n      align = 'inherit',\n      className,\n      component,\n      gutterBottom = false,\n      noWrap = false,\n      paragraph = false,\n      variant = 'body1',\n      variantMapping = defaultVariantMapping\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    align,\n    color,\n    className,\n    component,\n    gutterBottom,\n    noWrap,\n    paragraph,\n    variant,\n    variantMapping\n  });\n  const Component = component || (paragraph ? 'p' : variantMapping[variant] || defaultVariantMapping[variant]) || 'span';\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(TypographyRoot, _extends({\n    as: Component,\n    ref: ref,\n    ownerState: ownerState,\n    className: clsx(classes.root, className)\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? Typography.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * Set the text-align on the component.\n   * @default 'inherit'\n   */\n  align: PropTypes.oneOf(['center', 'inherit', 'justify', 'left', 'right']),\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the text will have a bottom margin.\n   * @default false\n   */\n  gutterBottom: PropTypes.bool,\n  /**\n   * If `true`, the text will not wrap, but instead will truncate with a text overflow ellipsis.\n   *\n   * Note that text overflow can only happen with block or inline-block level elements\n   * (the element needs to have a width in order to overflow).\n   * @default false\n   */\n  noWrap: PropTypes.bool,\n  /**\n   * If `true`, the element will be a paragraph element.\n   * @default false\n   */\n  paragraph: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Applies the theme typography styles.\n   * @default 'body1'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['body1', 'body2', 'button', 'caption', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'inherit', 'overline', 'subtitle1', 'subtitle2']), PropTypes.string]),\n  /**\n   * The component maps the variant prop to a range of different HTML element types.\n   * For instance, subtitle1 to `<h6>`.\n   * If you wish to change that mapping, you can provide your own.\n   * Alternatively, you can use the `component` prop.\n   * @default {\n   *   h1: 'h1',\n   *   h2: 'h2',\n   *   h3: 'h3',\n   *   h4: 'h4',\n   *   h5: 'h5',\n   *   h6: 'h6',\n   *   subtitle1: 'h6',\n   *   subtitle2: 'h6',\n   *   body1: 'p',\n   *   body2: 'p',\n   *   inherit: 'p',\n   * }\n   */\n  variantMapping: PropTypes /* @typescript-to-proptypes-ignore */.object\n} : void 0;\nexport default Typography;", "import { unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nimport generateUtilityClass from '../generateUtilityClass';\nexport function getIconButtonUtilityClass(slot) {\n  return generateUtilityClass('MuiIconButton', slot);\n}\nconst iconButtonClasses = generateUtilityClasses('MuiIconButton', ['root', 'disabled', 'colorInherit', 'colorPrimary', 'colorSecondary', 'colorError', 'colorInfo', 'colorSuccess', 'colorWarning', 'edgeStart', 'edgeEnd', 'sizeSmall', 'sizeMedium', 'sizeLarge']);\nexport default iconButtonClasses;", "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"edge\", \"children\", \"className\", \"color\", \"disabled\", \"disableFocusRipple\", \"size\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { chainPropTypes } from '@mui/utils';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport { alpha } from '@mui/system';\nimport styled from '../styles/styled';\nimport useThemeProps from '../styles/useThemeProps';\nimport ButtonBase from '../ButtonBase';\nimport capitalize from '../utils/capitalize';\nimport iconButtonClasses, { getIconButtonUtilityClass } from './iconButtonClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disabled,\n    color,\n    edge,\n    size\n  } = ownerState;\n  const slots = {\n    root: ['root', disabled && 'disabled', color !== 'default' && `color${capitalize(color)}`, edge && `edge${capitalize(edge)}`, `size${capitalize(size)}`]\n  };\n  return composeClasses(slots, getIconButtonUtilityClass, classes);\n};\nconst IconButtonRoot = styled(ButtonBase, {\n  name: 'MuiIconButton',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.color !== 'default' && styles[`color${capitalize(ownerState.color)}`], ownerState.edge && styles[`edge${capitalize(ownerState.edge)}`], styles[`size${capitalize(ownerState.size)}`]];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  textAlign: 'center',\n  flex: '0 0 auto',\n  fontSize: theme.typography.pxToRem(24),\n  padding: 8,\n  borderRadius: '50%',\n  overflow: 'visible',\n  // Explicitly set the default value to solve a bug on IE11.\n  color: (theme.vars || theme).palette.action.active,\n  transition: theme.transitions.create('background-color', {\n    duration: theme.transitions.duration.shortest\n  })\n}, !ownerState.disableRipple && {\n  '&:hover': {\n    backgroundColor: theme.vars ? `rgba(${theme.vars.palette.action.activeChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette.action.active, theme.palette.action.hoverOpacity),\n    // Reset on touch devices, it doesn't add specificity\n    '@media (hover: none)': {\n      backgroundColor: 'transparent'\n    }\n  }\n}, ownerState.edge === 'start' && {\n  marginLeft: ownerState.size === 'small' ? -3 : -12\n}, ownerState.edge === 'end' && {\n  marginRight: ownerState.size === 'small' ? -3 : -12\n}), ({\n  theme,\n  ownerState\n}) => {\n  var _palette;\n  const palette = (_palette = (theme.vars || theme).palette) == null ? void 0 : _palette[ownerState.color];\n  return _extends({}, ownerState.color === 'inherit' && {\n    color: 'inherit'\n  }, ownerState.color !== 'inherit' && ownerState.color !== 'default' && _extends({\n    color: palette == null ? void 0 : palette.main\n  }, !ownerState.disableRipple && {\n    '&:hover': _extends({}, palette && {\n      backgroundColor: theme.vars ? `rgba(${palette.mainChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(palette.main, theme.palette.action.hoverOpacity)\n    }, {\n      // Reset on touch devices, it doesn't add specificity\n      '@media (hover: none)': {\n        backgroundColor: 'transparent'\n      }\n    })\n  }), ownerState.size === 'small' && {\n    padding: 5,\n    fontSize: theme.typography.pxToRem(18)\n  }, ownerState.size === 'large' && {\n    padding: 12,\n    fontSize: theme.typography.pxToRem(28)\n  }, {\n    [`&.${iconButtonClasses.disabled}`]: {\n      backgroundColor: 'transparent',\n      color: (theme.vars || theme).palette.action.disabled\n    }\n  });\n});\n\n/**\n * Refer to the [Icons](/material-ui/icons/) section of the documentation\n * regarding the available icon options.\n */\nconst IconButton = /*#__PURE__*/React.forwardRef(function IconButton(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiIconButton'\n  });\n  const {\n      edge = false,\n      children,\n      className,\n      color = 'default',\n      disabled = false,\n      disableFocusRipple = false,\n      size = 'medium'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    edge,\n    color,\n    disabled,\n    disableFocusRipple,\n    size\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(IconButtonRoot, _extends({\n    className: clsx(classes.root, className),\n    centerRipple: true,\n    focusRipple: !disableFocusRipple,\n    disabled: disabled,\n    ref: ref,\n    ownerState: ownerState\n  }, other, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? IconButton.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * The icon to display.\n   */\n  children: chainPropTypes(PropTypes.node, props => {\n    const found = React.Children.toArray(props.children).some(child => /*#__PURE__*/React.isValidElement(child) && child.props.onClick);\n    if (found) {\n      return new Error(['MUI: You are providing an onClick event listener to a child of a button element.', 'Prefer applying it to the IconButton directly.', 'This guarantees that the whole <button> will be responsive to click events.'].join('\\n'));\n    }\n    return null;\n  }),\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#adding-new-colors).\n   * @default 'default'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['inherit', 'default', 'primary', 'secondary', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the  keyboard focus ripple is disabled.\n   * @default false\n   */\n  disableFocusRipple: PropTypes.bool,\n  /**\n   * If `true`, the ripple effect is disabled.\n   *\n   * ⚠️ Without a ripple there is no styling for :focus-visible by default. Be sure\n   * to highlight the element by applying separate styles with the `.Mui-focusVisible` class.\n   * @default false\n   */\n  disableRipple: PropTypes.bool,\n  /**\n   * If given, uses a negative margin to counteract the padding on one\n   * side (this is often helpful for aligning the left or right\n   * side of the icon with content above or below, without ruining the border\n   * size and shape).\n   * @default false\n   */\n  edge: PropTypes.oneOf(['end', 'start', false]),\n  /**\n   * The size of the component.\n   * `small` is equivalent to the dense button styling.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['small', 'medium', 'large']), PropTypes.string]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default IconButton;", "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"aria-describedby\", \"aria-labelledby\", \"BackdropComponent\", \"BackdropProps\", \"children\", \"className\", \"disableEscapeKeyDown\", \"fullScreen\", \"fullWidth\", \"maxWidth\", \"onBackdropClick\", \"onClose\", \"open\", \"PaperComponent\", \"PaperProps\", \"scroll\", \"TransitionComponent\", \"transitionDuration\", \"TransitionProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport { unstable_useId as useId } from '@mui/utils';\nimport capitalize from '../utils/capitalize';\nimport Modal from '../Modal';\nimport Fade from '../Fade';\nimport Paper from '../Paper';\nimport useThemeProps from '../styles/useThemeProps';\nimport styled from '../styles/styled';\nimport dialogClasses, { getDialogUtilityClass } from './dialogClasses';\nimport DialogContext from './DialogContext';\nimport Backdrop from '../Backdrop';\nimport useTheme from '../styles/useTheme';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DialogBackdrop = styled(Backdrop, {\n  name: 'MuiDialog',\n  slot: 'Backdrop',\n  overrides: (props, styles) => styles.backdrop\n})({\n  // Improve scrollable dialog support.\n  zIndex: -1\n});\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    scroll,\n    maxWidth,\n    fullWidth,\n    fullScreen\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    container: ['container', `scroll${capitalize(scroll)}`],\n    paper: ['paper', `paperScroll${capitalize(scroll)}`, `paperWidth${capitalize(String(maxWidth))}`, fullWidth && 'paperFullWidth', fullScreen && 'paperFullScreen']\n  };\n  return composeClasses(slots, getDialogUtilityClass, classes);\n};\nconst DialogRoot = styled(Modal, {\n  name: 'MuiDialog',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({\n  '@media print': {\n    // Use !important to override the Modal inline-style.\n    position: 'absolute !important'\n  }\n});\nconst DialogContainer = styled('div', {\n  name: 'MuiDialog',\n  slot: 'Container',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.container, styles[`scroll${capitalize(ownerState.scroll)}`]];\n  }\n})(({\n  ownerState\n}) => _extends({\n  height: '100%',\n  '@media print': {\n    height: 'auto'\n  },\n  // We disable the focus ring for mouse, touch and keyboard users.\n  outline: 0\n}, ownerState.scroll === 'paper' && {\n  display: 'flex',\n  justifyContent: 'center',\n  alignItems: 'center'\n}, ownerState.scroll === 'body' && {\n  overflowY: 'auto',\n  overflowX: 'hidden',\n  textAlign: 'center',\n  '&:after': {\n    content: '\"\"',\n    display: 'inline-block',\n    verticalAlign: 'middle',\n    height: '100%',\n    width: '0'\n  }\n}));\nconst DialogPaper = styled(Paper, {\n  name: 'MuiDialog',\n  slot: 'Paper',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.paper, styles[`scrollPaper${capitalize(ownerState.scroll)}`], styles[`paperWidth${capitalize(String(ownerState.maxWidth))}`], ownerState.fullWidth && styles.paperFullWidth, ownerState.fullScreen && styles.paperFullScreen];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  margin: 32,\n  position: 'relative',\n  overflowY: 'auto',\n  // Fix IE11 issue, to remove at some point.\n  '@media print': {\n    overflowY: 'visible',\n    boxShadow: 'none'\n  }\n}, ownerState.scroll === 'paper' && {\n  display: 'flex',\n  flexDirection: 'column',\n  maxHeight: 'calc(100% - 64px)'\n}, ownerState.scroll === 'body' && {\n  display: 'inline-block',\n  verticalAlign: 'middle',\n  textAlign: 'left' // 'initial' doesn't work on IE11\n}, !ownerState.maxWidth && {\n  maxWidth: 'calc(100% - 64px)'\n}, ownerState.maxWidth === 'xs' && {\n  maxWidth: theme.breakpoints.unit === 'px' ? Math.max(theme.breakpoints.values.xs, 444) : `${theme.breakpoints.values.xs}${theme.breakpoints.unit}`,\n  [`&.${dialogClasses.paperScrollBody}`]: {\n    [theme.breakpoints.down(Math.max(theme.breakpoints.values.xs, 444) + 32 * 2)]: {\n      maxWidth: 'calc(100% - 64px)'\n    }\n  }\n}, ownerState.maxWidth && ownerState.maxWidth !== 'xs' && {\n  maxWidth: `${theme.breakpoints.values[ownerState.maxWidth]}${theme.breakpoints.unit}`,\n  [`&.${dialogClasses.paperScrollBody}`]: {\n    [theme.breakpoints.down(theme.breakpoints.values[ownerState.maxWidth] + 32 * 2)]: {\n      maxWidth: 'calc(100% - 64px)'\n    }\n  }\n}, ownerState.fullWidth && {\n  width: 'calc(100% - 64px)'\n}, ownerState.fullScreen && {\n  margin: 0,\n  width: '100%',\n  maxWidth: '100%',\n  height: '100%',\n  maxHeight: 'none',\n  borderRadius: 0,\n  [`&.${dialogClasses.paperScrollBody}`]: {\n    margin: 0,\n    maxWidth: '100%'\n  }\n}));\n\n/**\n * Dialogs are overlaid modal paper based components with a backdrop.\n */\nconst Dialog = /*#__PURE__*/React.forwardRef(function Dialog(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiDialog'\n  });\n  const theme = useTheme();\n  const defaultTransitionDuration = {\n    enter: theme.transitions.duration.enteringScreen,\n    exit: theme.transitions.duration.leavingScreen\n  };\n  const {\n      'aria-describedby': ariaDescribedby,\n      'aria-labelledby': ariaLabelledbyProp,\n      BackdropComponent,\n      BackdropProps,\n      children,\n      className,\n      disableEscapeKeyDown = false,\n      fullScreen = false,\n      fullWidth = false,\n      maxWidth = 'sm',\n      onBackdropClick,\n      onClose,\n      open,\n      PaperComponent = Paper,\n      PaperProps = {},\n      scroll = 'paper',\n      TransitionComponent = Fade,\n      transitionDuration = defaultTransitionDuration,\n      TransitionProps\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    disableEscapeKeyDown,\n    fullScreen,\n    fullWidth,\n    maxWidth,\n    scroll\n  });\n  const classes = useUtilityClasses(ownerState);\n  const backdropClick = React.useRef();\n  const handleMouseDown = event => {\n    // We don't want to close the dialog when clicking the dialog content.\n    // Make sure the event starts and ends on the same DOM element.\n    backdropClick.current = event.target === event.currentTarget;\n  };\n  const handleBackdropClick = event => {\n    // Ignore the events not coming from the \"backdrop\".\n    if (!backdropClick.current) {\n      return;\n    }\n    backdropClick.current = null;\n    if (onBackdropClick) {\n      onBackdropClick(event);\n    }\n    if (onClose) {\n      onClose(event, 'backdropClick');\n    }\n  };\n  const ariaLabelledby = useId(ariaLabelledbyProp);\n  const dialogContextValue = React.useMemo(() => {\n    return {\n      titleId: ariaLabelledby\n    };\n  }, [ariaLabelledby]);\n  return /*#__PURE__*/_jsx(DialogRoot, _extends({\n    className: clsx(classes.root, className),\n    closeAfterTransition: true,\n    components: {\n      Backdrop: DialogBackdrop\n    },\n    componentsProps: {\n      backdrop: _extends({\n        transitionDuration,\n        as: BackdropComponent\n      }, BackdropProps)\n    },\n    disableEscapeKeyDown: disableEscapeKeyDown,\n    onClose: onClose,\n    open: open,\n    ref: ref,\n    onClick: handleBackdropClick,\n    ownerState: ownerState\n  }, other, {\n    children: /*#__PURE__*/_jsx(TransitionComponent, _extends({\n      appear: true,\n      in: open,\n      timeout: transitionDuration,\n      role: \"presentation\"\n    }, TransitionProps, {\n      children: /*#__PURE__*/_jsx(DialogContainer, {\n        className: clsx(classes.container),\n        onMouseDown: handleMouseDown,\n        ownerState: ownerState,\n        children: /*#__PURE__*/_jsx(DialogPaper, _extends({\n          as: PaperComponent,\n          elevation: 24,\n          role: \"dialog\",\n          \"aria-describedby\": ariaDescribedby,\n          \"aria-labelledby\": ariaLabelledby\n        }, PaperProps, {\n          className: clsx(classes.paper, PaperProps.className),\n          ownerState: ownerState,\n          children: /*#__PURE__*/_jsx(DialogContext.Provider, {\n            value: dialogContextValue,\n            children: children\n          })\n        }))\n      })\n    }))\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Dialog.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * The id(s) of the element(s) that describe the dialog.\n   */\n  'aria-describedby': PropTypes.string,\n  /**\n   * The id(s) of the element(s) that label the dialog.\n   */\n  'aria-labelledby': PropTypes.string,\n  /**\n   * A backdrop component. This prop enables custom backdrop rendering.\n   * @deprecated Use `slots.backdrop` instead. While this prop currently works, it will be removed in the next major version.\n   * Use the `slots.backdrop` prop to make your application ready for the next version of Material UI.\n   * @default styled(Backdrop, {\n   *   name: 'MuiModal',\n   *   slot: 'Backdrop',\n   *   overridesResolver: (props, styles) => {\n   *     return styles.backdrop;\n   *   },\n   * })({\n   *   zIndex: -1,\n   * })\n   */\n  BackdropComponent: PropTypes.elementType,\n  /**\n   * @ignore\n   */\n  BackdropProps: PropTypes.object,\n  /**\n   * Dialog children, usually the included sub-components.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, hitting escape will not fire the `onClose` callback.\n   * @default false\n   */\n  disableEscapeKeyDown: PropTypes.bool,\n  /**\n   * If `true`, the dialog is full-screen.\n   * @default false\n   */\n  fullScreen: PropTypes.bool,\n  /**\n   * If `true`, the dialog stretches to `maxWidth`.\n   *\n   * Notice that the dialog width grow is limited by the default margin.\n   * @default false\n   */\n  fullWidth: PropTypes.bool,\n  /**\n   * Determine the max-width of the dialog.\n   * The dialog width grows with the size of the screen.\n   * Set to `false` to disable `maxWidth`.\n   * @default 'sm'\n   */\n  maxWidth: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl', false]), PropTypes.string]),\n  /**\n   * Callback fired when the backdrop is clicked.\n   * @deprecated Use the `onClose` prop with the `reason` argument to handle the `backdropClick` events.\n   */\n  onBackdropClick: PropTypes.func,\n  /**\n   * Callback fired when the component requests to be closed.\n   *\n   * @param {object} event The event source of the callback.\n   * @param {string} reason Can be: `\"escapeKeyDown\"`, `\"backdropClick\"`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool.isRequired,\n  /**\n   * The component used to render the body of the dialog.\n   * @default Paper\n   */\n  PaperComponent: PropTypes.elementType,\n  /**\n   * Props applied to the [`Paper`](/material-ui/api/paper/) element.\n   * @default {}\n   */\n  PaperProps: PropTypes.object,\n  /**\n   * Determine the container for scrolling the dialog.\n   * @default 'paper'\n   */\n  scroll: PropTypes.oneOf(['body', 'paper']),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The component used for the transition.\n   * [Follow this guide](/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.\n   * @default Fade\n   */\n  TransitionComponent: PropTypes.elementType,\n  /**\n   * The duration for the transition, in milliseconds.\n   * You may specify a single timeout for all transitions, or individually with an object.\n   * @default {\n   *   enter: theme.transitions.duration.enteringScreen,\n   *   exit: theme.transitions.duration.leavingScreen,\n   * }\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * Props applied to the transition element.\n   * By default, the element is based on this [`Transition`](http://reactcommunity.org/react-transition-group/transition/) component.\n   */\n  TransitionProps: PropTypes.object\n} : void 0;\nexport default Dialog;", "import { createMotionProxy } from './motion-proxy.js';\nimport { createDomMotionConfig } from './utils/create-config.js';\n\n/**\n * @public\n */\nvar m = createMotionProxy(createDomMotionConfig);\n\nexport { m };\n", "import { __read, __rest } from 'tslib';\nimport * as React from 'react';\nimport { useState, useRef, useEffect } from 'react';\nimport { LazyContext } from '../../context/LazyContext.js';\nimport { loadFeatures } from '../../motion/features/definitions.js';\n\n/**\n * Used in conjunction with the `m` component to reduce bundle size.\n *\n * `m` is a version of the `motion` component that only loads functionality\n * critical for the initial render.\n *\n * `LazyMotion` can then be used to either synchronously or asynchronously\n * load animation and gesture support.\n *\n * ```jsx\n * // Synchronous loading\n * import { LazyMotion, m, domAnimations } from \"framer-motion\"\n *\n * function App() {\n *   return (\n *     <LazyMotion features={domAnimations}>\n *       <m.div animate={{ scale: 2 }} />\n *     </LazyMotion>\n *   )\n * }\n *\n * // Asynchronous loading\n * import { LazyMotion, m } from \"framer-motion\"\n *\n * function App() {\n *   return (\n *     <LazyMotion features={() => import('./path/to/domAnimations')}>\n *       <m.div animate={{ scale: 2 }} />\n *     </LazyMotion>\n *   )\n * }\n * ```\n *\n * @public\n */\nfunction LazyMotion(_a) {\n    var children = _a.children, features = _a.features, _b = _a.strict, strict = _b === void 0 ? false : _b;\n    var _c = __read(useState(!isLazyBundle(features)), 2), setIsLoaded = _c[1];\n    var loadedRenderer = useRef(undefined);\n    /**\n     * If this is a synchronous load, load features immediately\n     */\n    if (!isLazyBundle(features)) {\n        var renderer = features.renderer, loadedFeatures = __rest(features, [\"renderer\"]);\n        loadedRenderer.current = renderer;\n        loadFeatures(loadedFeatures);\n    }\n    useEffect(function () {\n        if (isLazyBundle(features)) {\n            features().then(function (_a) {\n                var renderer = _a.renderer, loadedFeatures = __rest(_a, [\"renderer\"]);\n                loadFeatures(loadedFeatures);\n                loadedRenderer.current = renderer;\n                setIsLoaded(true);\n            });\n        }\n    }, []);\n    return (React.createElement(LazyContext.Provider, { value: { renderer: loadedRenderer.current, strict: strict } }, children));\n}\nfunction isLazyBundle(features) {\n    return typeof features === \"function\";\n}\n\nexport { LazyMotion };\n", "import * as React from 'react';\nimport { useMemo } from 'react';\nimport { PresenceContext } from '../../context/PresenceContext.js';\nimport { useConstant } from '../../utils/use-constant.js';\n\nvar presenceId = 0;\nfunction getPresenceId() {\n    var id = presenceId;\n    presenceId++;\n    return id;\n}\nvar PresenceChild = function (_a) {\n    var children = _a.children, initial = _a.initial, isPresent = _a.isPresent, onExitComplete = _a.onExitComplete, custom = _a.custom, presenceAffectsLayout = _a.presenceAffectsLayout;\n    var presenceChildren = useConstant(newChildrenMap);\n    var id = useConstant(getPresenceId);\n    var context = useMemo(function () { return ({\n        id: id,\n        initial: initial,\n        isPresent: isPresent,\n        custom: custom,\n        onExitComplete: function (childId) {\n            presenceChildren.set(childId, true);\n            var allComplete = true;\n            presenceChildren.forEach(function (isComplete) {\n                if (!isComplete)\n                    allComplete = false;\n            });\n            allComplete && (onExitComplete === null || onExitComplete === void 0 ? void 0 : onExitComplete());\n        },\n        register: function (childId) {\n            presenceChildren.set(childId, false);\n            return function () { return presenceChildren.delete(childId); };\n        },\n    }); }, \n    /**\n     * If the presence of a child affects the layout of the components around it,\n     * we want to make a new context value to ensure they get re-rendered\n     * so they can detect that layout change.\n     */\n    presenceAffectsLayout ? undefined : [isPresent]);\n    useMemo(function () {\n        presenceChildren.forEach(function (_, key) { return presenceChildren.set(key, false); });\n    }, [isPresent]);\n    /**\n     * If there's no `motion` components to fire exit animations, we want to remove this\n     * component immediately.\n     */\n    React.useEffect(function () {\n        !isPresent && !presenceChildren.size && (onExitComplete === null || onExitComplete === void 0 ? void 0 : onExitComplete());\n    }, [isPresent]);\n    return (React.createElement(PresenceContext.Provider, { value: context }, children));\n};\nfunction newChildrenMap() {\n    return new Map();\n}\n\nexport { PresenceChild };\n", "import { __spreadArray, __read } from 'tslib';\nimport * as React from 'react';\nimport { useContext, useRef, cloneElement, Children, isValidElement } from 'react';\nimport { useForceUpdate } from '../../utils/use-force-update.js';\nimport { PresenceChild } from './PresenceChild.js';\nimport { SharedLayoutContext, isSharedLayout } from '../../context/SharedLayoutContext.js';\n\nfunction getChildKey(child) {\n    return child.key || \"\";\n}\nfunction updateChildLookup(children, allChildren) {\n    var seenChildren = process.env.NODE_ENV !== \"production\" ? new Set() : null;\n    children.forEach(function (child) {\n        var key = getChildKey(child);\n        if (process.env.NODE_ENV !== \"production\" && seenChildren) {\n            if (seenChildren.has(key)) {\n                console.warn(\"Children of AnimatePresence require unique keys. \\\"\" + key + \"\\\" is a duplicate.\");\n            }\n            seenChildren.add(key);\n        }\n        allChildren.set(key, child);\n    });\n}\nfunction onlyElements(children) {\n    var filtered = [];\n    // We use forEach here instead of map as map mutates the component key by preprending `.$`\n    Children.forEach(children, function (child) {\n        if (isValidElement(child))\n            filtered.push(child);\n    });\n    return filtered;\n}\n/**\n * `AnimatePresence` enables the animation of components that have been removed from the tree.\n *\n * When adding/removing more than a single child, every child **must** be given a unique `key` prop.\n *\n * @library\n *\n * Any `Frame` components that have an `exit` property defined will animate out when removed from\n * the tree.\n *\n * ```jsx\n * import { Frame, AnimatePresence } from 'framer'\n *\n * // As items are added and removed from `items`\n * export function Items({ items }) {\n *   return (\n *     <AnimatePresence>\n *       {items.map(item => (\n *         <Frame\n *           key={item.id}\n *           initial={{ opacity: 0 }}\n *           animate={{ opacity: 1 }}\n *           exit={{ opacity: 0 }}\n *         />\n *       ))}\n *     </AnimatePresence>\n *   )\n * }\n * ```\n *\n * You can sequence exit animations throughout a tree using variants.\n *\n * @motion\n *\n * Any `motion` components that have an `exit` property defined will animate out when removed from\n * the tree.\n *\n * ```jsx\n * import { motion, AnimatePresence } from 'framer-motion'\n *\n * export const Items = ({ items }) => (\n *   <AnimatePresence>\n *     {items.map(item => (\n *       <motion.div\n *         key={item.id}\n *         initial={{ opacity: 0 }}\n *         animate={{ opacity: 1 }}\n *         exit={{ opacity: 0 }}\n *       />\n *     ))}\n *   </AnimatePresence>\n * )\n * ```\n *\n * You can sequence exit animations throughout a tree using variants.\n *\n * If a child contains multiple `motion` components with `exit` props, it will only unmount the child\n * once all `motion` components have finished animating out. Likewise, any components using\n * `usePresence` all need to call `safeToRemove`.\n *\n * @public\n */\nvar AnimatePresence = function (_a) {\n    var children = _a.children, custom = _a.custom, _b = _a.initial, initial = _b === void 0 ? true : _b, onExitComplete = _a.onExitComplete, exitBeforeEnter = _a.exitBeforeEnter, _c = _a.presenceAffectsLayout, presenceAffectsLayout = _c === void 0 ? true : _c;\n    // We want to force a re-render once all exiting animations have finished. We\n    // either use a local forceRender function, or one from a parent context if it exists.\n    var forceRender = useForceUpdate();\n    var layoutContext = useContext(SharedLayoutContext);\n    if (isSharedLayout(layoutContext)) {\n        forceRender = layoutContext.forceUpdate;\n    }\n    var isInitialRender = useRef(true);\n    // Filter out any children that aren't ReactElements. We can only track ReactElements with a props.key\n    var filteredChildren = onlyElements(children);\n    // Keep a living record of the children we're actually rendering so we\n    // can diff to figure out which are entering and exiting\n    var presentChildren = useRef(filteredChildren);\n    // A lookup table to quickly reference components by key\n    var allChildren = useRef(new Map())\n        .current;\n    // A living record of all currently exiting components.\n    var exiting = useRef(new Set()).current;\n    updateChildLookup(filteredChildren, allChildren);\n    // If this is the initial component render, just deal with logic surrounding whether\n    // we play onMount animations or not.\n    if (isInitialRender.current) {\n        isInitialRender.current = false;\n        return (React.createElement(React.Fragment, null, filteredChildren.map(function (child) { return (React.createElement(PresenceChild, { key: getChildKey(child), isPresent: true, initial: initial ? undefined : false, presenceAffectsLayout: presenceAffectsLayout }, child)); })));\n    }\n    // If this is a subsequent render, deal with entering and exiting children\n    var childrenToRender = __spreadArray([], __read(filteredChildren));\n    // Diff the keys of the currently-present and target children to update our\n    // exiting list.\n    var presentKeys = presentChildren.current.map(getChildKey);\n    var targetKeys = filteredChildren.map(getChildKey);\n    // Diff the present children with our target children and mark those that are exiting\n    var numPresent = presentKeys.length;\n    for (var i = 0; i < numPresent; i++) {\n        var key = presentKeys[i];\n        if (targetKeys.indexOf(key) === -1) {\n            exiting.add(key);\n        }\n        else {\n            // In case this key has re-entered, remove from the exiting list\n            exiting.delete(key);\n        }\n    }\n    // If we currently have exiting children, and we're deferring rendering incoming children\n    // until after all current children have exiting, empty the childrenToRender array\n    if (exitBeforeEnter && exiting.size) {\n        childrenToRender = [];\n    }\n    // Loop through all currently exiting components and clone them to overwrite `animate`\n    // with any `exit` prop they might have defined.\n    exiting.forEach(function (key) {\n        // If this component is actually entering again, early return\n        if (targetKeys.indexOf(key) !== -1)\n            return;\n        var child = allChildren.get(key);\n        if (!child)\n            return;\n        var insertionIndex = presentKeys.indexOf(key);\n        var onExit = function () {\n            allChildren.delete(key);\n            exiting.delete(key);\n            // Remove this child from the present children\n            var removeIndex = presentChildren.current.findIndex(function (presentChild) { return presentChild.key === key; });\n            presentChildren.current.splice(removeIndex, 1);\n            // Defer re-rendering until all exiting children have indeed left\n            if (!exiting.size) {\n                presentChildren.current = filteredChildren;\n                forceRender();\n                onExitComplete && onExitComplete();\n            }\n        };\n        childrenToRender.splice(insertionIndex, 0, React.createElement(PresenceChild, { key: getChildKey(child), isPresent: false, onExitComplete: onExit, custom: custom, presenceAffectsLayout: presenceAffectsLayout }, child));\n    });\n    // Add `MotionContext` even to children that don't need it to ensure we're rendering\n    // the same tree between renders\n    childrenToRender = childrenToRender.map(function (child) {\n        var key = child.key;\n        return exiting.has(key) ? (child) : (React.createElement(PresenceChild, { key: getChildKey(child), isPresent: true, presenceAffectsLayout: presenceAffectsLayout }, child));\n    });\n    presentChildren.current = childrenToRender;\n    if (process.env.NODE_ENV !== \"production\" &&\n        exitBeforeEnter &&\n        childrenToRender.length > 1) {\n        console.warn(\"You're attempting to animate multiple children within AnimatePresence, but its exitBeforeEnter prop is set to true. This will lead to odd visual behaviour.\");\n    }\n    return (React.createElement(React.Fragment, null, exiting.size\n        ? childrenToRender\n        : childrenToRender.map(function (child) { return cloneElement(child); })));\n};\n\nexport { AnimatePresence };\n", "import { __read } from 'tslib';\nimport { useCallback, useRef, useState } from 'react';\nimport { useUnmountEffect } from './use-unmount-effect.js';\n\nfunction useForceUpdate() {\n    var unloadingRef = useRef(false);\n    var _a = __read(useState(0), 2), forcedRenderCount = _a[0], setForcedRenderCount = _a[1];\n    useUnmountEffect(function () { return (unloadingRef.current = true); });\n    return useCallback(function () {\n        !unloadingRef.current && setForcedRenderCount(forcedRenderCount + 1);\n    }, [forcedRenderCount]);\n}\n\nexport { useForceUpdate };\n", "import { __spreadArray, __read } from 'tslib';\nimport { invariant } from 'hey-listen';\nimport { stopAnimation, animateVisualElement } from '../render/utils/animation.js';\nimport { setValues } from '../render/utils/setters.js';\n\n/**\n * @public\n */\nfunction animationControls() {\n    /**\n     * Track whether the host component has mounted.\n     */\n    var hasMounted = false;\n    /**\n     * Pending animations that are started before a component is mounted.\n     * TODO: Remove this as animations should only run in effects\n     */\n    var pendingAnimations = [];\n    /**\n     * A collection of linked component animation controls.\n     */\n    var subscribers = new Set();\n    var controls = {\n        subscribe: function (visualElement) {\n            subscribers.add(visualElement);\n            return function () { return void subscribers.delete(visualElement); };\n        },\n        start: function (definition, transitionOverride) {\n            /**\n             * TODO: We only perform this hasMounted check because in Framer we used to\n             * encourage the ability to start an animation within the render phase. This\n             * isn't behaviour concurrent-safe so when we make Framer concurrent-safe\n             * we can ditch this.\n             */\n            if (hasMounted) {\n                var animations_1 = [];\n                subscribers.forEach(function (visualElement) {\n                    animations_1.push(animateVisualElement(visualElement, definition, {\n                        transitionOverride: transitionOverride,\n                    }));\n                });\n                return Promise.all(animations_1);\n            }\n            else {\n                return new Promise(function (resolve) {\n                    pendingAnimations.push({\n                        animation: [definition, transitionOverride],\n                        resolve: resolve,\n                    });\n                });\n            }\n        },\n        set: function (definition) {\n            invariant(hasMounted, \"controls.set() should only be called after a component has mounted. Consider calling within a useEffect hook.\");\n            return subscribers.forEach(function (visualElement) {\n                setValues(visualElement, definition);\n            });\n        },\n        stop: function () {\n            subscribers.forEach(function (visualElement) {\n                stopAnimation(visualElement);\n            });\n        },\n        mount: function () {\n            hasMounted = true;\n            pendingAnimations.forEach(function (_a) {\n                var animation = _a.animation, resolve = _a.resolve;\n                controls.start.apply(controls, __spreadArray([], __read(animation))).then(resolve);\n            });\n            return function () {\n                hasMounted = false;\n                controls.stop();\n            };\n        },\n    };\n    return controls;\n}\n\nexport { animationControls };\n", "import { animationControls } from './animation-controls.js';\nimport { useEffect } from 'react';\nimport { useConstant } from '../utils/use-constant.js';\n\n/**\n * Creates `AnimationControls`, which can be used to manually start, stop\n * and sequence animations on one or more components.\n *\n * The returned `AnimationControls` should be passed to the `animate` property\n * of the components you want to animate.\n *\n * These components can then be animated with the `start` method.\n *\n * @library\n *\n * ```jsx\n * import * as React from 'react'\n * import { Frame, useAnimation } from 'framer'\n *\n * export function MyComponent(props) {\n *    const controls = useAnimation()\n *\n *    controls.start({\n *        x: 100,\n *        transition: { duration: 0.5 },\n *    })\n *\n *    return <Frame animate={controls} />\n * }\n * ```\n *\n * @motion\n *\n * ```jsx\n * import * as React from 'react'\n * import { motion, useAnimation } from 'framer-motion'\n *\n * export function MyComponent(props) {\n *    const controls = useAnimation()\n *\n *    controls.start({\n *        x: 100,\n *        transition: { duration: 0.5 },\n *    })\n *\n *    return <motion.div animate={controls} />\n * }\n * ```\n *\n * @returns Animation controller with `start` and `stop` methods\n *\n * @public\n */\nfunction useAnimation() {\n    var controls = useConstant(animationControls);\n    useEffect(controls.mount, []);\n    return controls;\n}\n\nexport { useAnimation };\n", "import { unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nimport generateUtilityClass from '../generateUtilityClass';\nexport function getFabUtilityClass(slot) {\n  return generateUtilityClass('MuiFab', slot);\n}\nconst fabClasses = generateUtilityClasses('MuiFab', ['root', 'primary', 'secondary', 'extended', 'circular', 'focusVisible', 'disabled', 'colorInherit', 'sizeSmall', 'sizeMedium', 'sizeLarge', 'info', 'error', 'warning', 'success']);\nexport default fabClasses;", "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"color\", \"component\", \"disabled\", \"disableFocusRipple\", \"focusVisibleClassName\", \"size\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport ButtonBase from '../ButtonBase';\nimport capitalize from '../utils/capitalize';\nimport useThemeProps from '../styles/useThemeProps';\nimport fabClasses, { getFabUtilityClass } from './fabClasses';\nimport styled, { rootShouldForwardProp } from '../styles/styled';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    color,\n    variant,\n    classes,\n    size\n  } = ownerState;\n  const slots = {\n    root: ['root', variant, `size${capitalize(size)}`, color === 'inherit' ? 'colorInherit' : color]\n  };\n  const composedClasses = composeClasses(slots, getFabUtilityClass, classes);\n  return _extends({}, classes, composedClasses);\n};\nconst FabRoot = styled(ButtonBase, {\n  name: 'MuiFab',\n  slot: 'Root',\n  shouldForwardProp: prop => rootShouldForwardProp(prop) || prop === 'classes',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.variant], styles[`size${capitalize(ownerState.size)}`], ownerState.color === 'inherit' && styles.colorInherit, styles[capitalize(ownerState.size)], styles[ownerState.color]];\n  }\n})(({\n  theme,\n  ownerState\n}) => {\n  var _theme$palette$getCon, _theme$palette;\n  return _extends({}, theme.typography.button, {\n    minHeight: 36,\n    transition: theme.transitions.create(['background-color', 'box-shadow', 'border-color'], {\n      duration: theme.transitions.duration.short\n    }),\n    borderRadius: '50%',\n    padding: 0,\n    minWidth: 0,\n    width: 56,\n    height: 56,\n    zIndex: (theme.vars || theme).zIndex.fab,\n    boxShadow: (theme.vars || theme).shadows[6],\n    '&:active': {\n      boxShadow: (theme.vars || theme).shadows[12]\n    },\n    color: theme.vars ? theme.vars.palette.text.primary : (_theme$palette$getCon = (_theme$palette = theme.palette).getContrastText) == null ? void 0 : _theme$palette$getCon.call(_theme$palette, theme.palette.grey[300]),\n    backgroundColor: (theme.vars || theme).palette.grey[300],\n    '&:hover': {\n      backgroundColor: (theme.vars || theme).palette.grey.A100,\n      // Reset on touch devices, it doesn't add specificity\n      '@media (hover: none)': {\n        backgroundColor: (theme.vars || theme).palette.grey[300]\n      },\n      textDecoration: 'none'\n    },\n    [`&.${fabClasses.focusVisible}`]: {\n      boxShadow: (theme.vars || theme).shadows[6]\n    }\n  }, ownerState.size === 'small' && {\n    width: 40,\n    height: 40\n  }, ownerState.size === 'medium' && {\n    width: 48,\n    height: 48\n  }, ownerState.variant === 'extended' && {\n    borderRadius: 48 / 2,\n    padding: '0 16px',\n    width: 'auto',\n    minHeight: 'auto',\n    minWidth: 48,\n    height: 48\n  }, ownerState.variant === 'extended' && ownerState.size === 'small' && {\n    width: 'auto',\n    padding: '0 8px',\n    borderRadius: 34 / 2,\n    minWidth: 34,\n    height: 34\n  }, ownerState.variant === 'extended' && ownerState.size === 'medium' && {\n    width: 'auto',\n    padding: '0 16px',\n    borderRadius: 40 / 2,\n    minWidth: 40,\n    height: 40\n  }, ownerState.color === 'inherit' && {\n    color: 'inherit'\n  });\n}, ({\n  theme,\n  ownerState\n}) => _extends({}, ownerState.color !== 'inherit' && ownerState.color !== 'default' && (theme.vars || theme).palette[ownerState.color] != null && {\n  color: (theme.vars || theme).palette[ownerState.color].contrastText,\n  backgroundColor: (theme.vars || theme).palette[ownerState.color].main,\n  '&:hover': {\n    backgroundColor: (theme.vars || theme).palette[ownerState.color].dark,\n    // Reset on touch devices, it doesn't add specificity\n    '@media (hover: none)': {\n      backgroundColor: (theme.vars || theme).palette[ownerState.color].main\n    }\n  }\n}), ({\n  theme\n}) => ({\n  [`&.${fabClasses.disabled}`]: {\n    color: (theme.vars || theme).palette.action.disabled,\n    boxShadow: (theme.vars || theme).shadows[0],\n    backgroundColor: (theme.vars || theme).palette.action.disabledBackground\n  }\n}));\nconst Fab = /*#__PURE__*/React.forwardRef(function Fab(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiFab'\n  });\n  const {\n      children,\n      className,\n      color = 'default',\n      component = 'button',\n      disabled = false,\n      disableFocusRipple = false,\n      focusVisibleClassName,\n      size = 'large',\n      variant = 'circular'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    color,\n    component,\n    disabled,\n    disableFocusRipple,\n    size,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(FabRoot, _extends({\n    className: clsx(classes.root, className),\n    component: component,\n    disabled: disabled,\n    focusRipple: !disableFocusRipple,\n    focusVisibleClassName: clsx(classes.focusVisible, focusVisibleClassName),\n    ownerState: ownerState,\n    ref: ref\n  }, other, {\n    classes: classes,\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Fab.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#adding-new-colors).\n   * @default 'default'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['default', 'error', 'info', 'inherit', 'primary', 'secondary', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the  keyboard focus ripple is disabled.\n   * @default false\n   */\n  disableFocusRipple: PropTypes.bool,\n  /**\n   * If `true`, the ripple effect is disabled.\n   */\n  disableRipple: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  focusVisibleClassName: PropTypes.string,\n  /**\n   * The URL to link to when the button is clicked.\n   * If defined, an `a` element will be used as the root node.\n   */\n  href: PropTypes.string,\n  /**\n   * The size of the component.\n   * `small` is equivalent to the dense button styling.\n   * @default 'large'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['small', 'medium', 'large']), PropTypes.string]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   * @default 'circular'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['circular', 'extended']), PropTypes.string])\n} : void 0;\nexport default Fab;"], "sourceRoot": ""}